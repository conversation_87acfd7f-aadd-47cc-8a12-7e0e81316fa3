#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的MMIF-CDDFuse融合网络测试
"""

import torch
import torch.nn.functional as F
import warnings
warnings.filterwarnings('ignore')

def test_mmif_basic():
    """基本功能测试"""
    print("=" * 50)
    print("MMIF-CDDFuse融合网络基本测试")
    print("=" * 50)
    
    # 导入模型
    from models.mmif_fusion_net import MMIFFusionNet
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建模型
    model = MMIFFusionNet(
        s2_channels=4,
        s1_channels=2,
        hidden_dim=64
    ).to(device)
    
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试输入
    batch_size = 2
    s2_input = torch.randn(batch_size, 4, 128, 128).to(device)
    s1_input = torch.randn(batch_size, 2, 128, 128).to(device)
    
    print(f"\n输入形状:")
    print(f"Sentinel-2: {s2_input.shape}")
    print(f"Sentinel-1: {s1_input.shape}")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        fused, base, detail = model(s2_input, s1_input)
    
    print(f"\n输出形状:")
    print(f"融合特征: {fused.shape}")
    print(f"基础特征: {base.shape}")
    print(f"细节特征: {detail.shape}")
    
    # 测试相关性损失
    with torch.no_grad():
        s2_fused, s2_base, s2_detail = model(s2_input, torch.zeros_like(s1_input))
        s1_fused, s1_base, s1_detail = model(torch.zeros_like(s2_input), s1_input)
    
    corr_loss = model.get_correlation_loss(s2_base, s1_base, s2_detail, s1_detail)
    print(f"\n相关性损失: {corr_loss.item():.4f}")
    
    print("✓ 基本功能测试通过")


def test_integration():
    """集成测试"""
    print("\n" + "=" * 50)
    print("弱监督分割网络集成测试")
    print("=" * 50)
    
    from models.weakly_net import create_weakly_segmentation_net
    
    # 简化配置
    config = {
        'data': {'num_classes': 2},
        'model': {
            'fusion': {
                'fusion_type': 'mmif_cddfuse',
                's2_channels': 4,
                's1_channels': 2,
                'hidden_dim': 64,
                'num_blocks': [2, 2],  # 减少块数以加快测试
                'heads': [4, 4, 4],
                'ffn_expansion_factor': 2,
                'bias': False
            },
                         'weakly_supervised': {
                 'dual_classifier': True,   # 重新启用双分类器
                 'region_growing': {
                     'max_iterations': 3,
                     'confidence_threshold': 0.9,
                     'neighbor_size': 8,
                     'morphology_kernel': 3
                 }
             }
        }
    }
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建模型
    model = create_weakly_segmentation_net(config).to(device)
    print(f"集成模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试输入
    batch_size = 1  # 减少批大小
    s2_input = torch.randn(batch_size, 4, 64, 64).to(device)  # 减少图像大小
    s1_input = torch.randn(batch_size, 2, 64, 64).to(device)
    point_labels = torch.randint(0, 2, (batch_size, 64, 64)).to(device)
    
    # 设置大部分像素为未标记
    mask = torch.rand(batch_size, 64, 64) > 0.02
    point_labels[mask] = 255
    
    print(f"\n输入数据:")
    print(f"Sentinel-2: {s2_input.shape}")
    print(f"Sentinel-1: {s1_input.shape}")
    print(f"有标签像素比例: {(point_labels != 255).float().mean():.4f}")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        results = model(s2_input, s1_input, training=False)
    
    print(f"\n模型输出:")
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")
    
    print("✓ 集成测试通过")


def main():
    """主测试函数"""
    print("MMIF-CDDFuse融合网络简化测试")
    print("=" * 60)
    
    try:
        # 基本功能测试
        test_mmif_basic()
        
        # 集成测试
        test_integration()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！MMIF-CDDFuse融合网络集成成功！")
        print("=" * 60)
        
        print("\n主要特性:")
        print("✓ 基于CDDFuse的相关性驱动双分支特征分解")
        print("✓ Transformer + INN混合架构")
        print("✓ 基础特征相关性约束")
        print("✓ 细节特征去相关性约束")
        print("✓ 与弱监督分割网络完美集成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 