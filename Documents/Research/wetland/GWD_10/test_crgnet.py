#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CRGNet测试脚本
验证基于CRGNet的弱监督分割网络功能
"""

import torch
import torch.nn.functional as F
import warnings
warnings.filterwarnings('ignore')

def test_crgnet_basic():
    """测试CRGNet基本功能"""
    print("=" * 60)
    print("CRGNet基本功能测试")
    print("=" * 60)
    
    from models.crgnet import create_crgnet, create_crgnet_loss
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建配置
    config = {
        'data': {'num_classes': 4},
        'model': {
            'fusion': {
                'fusion_type': 'mmif_cddfuse',
                's2_channels': 4,
                's1_channels': 2,
                'hidden_dim': 64,
                'num_blocks': [2, 2],
                'heads': [4, 4, 4],
                'ffn_expansion_factor': 2,
                'bias': False,
                'confidence_threshold': 0.95,
                'max_iterations': 5,
                'morphology_kernel': 3,
                'boundary_removal_kernel': 8
            }
        },
        'training': {
            'loss_weights': {
                'consistency': 1.0
            }
        }
    }
    
    # 创建模型
    model = create_crgnet(config).to(device)
    criterion = create_crgnet_loss(config)
    
    print(f"CRGNet参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 模拟输入
    batch_size = 2
    s2_input = torch.randn(batch_size, 4, 128, 128).to(device)
    s1_input = torch.randn(batch_size, 2, 128, 128).to(device)
    point_labels = torch.randint(0, 4, (batch_size, 128, 128)).to(device)
    
    # 设置大部分像素为未标记
    mask = torch.rand(batch_size, 128, 128) > 0.02
    point_labels[mask] = 255
    
    print(f"\n输入数据:")
    print(f"Sentinel-2: {s2_input.shape}")
    print(f"Sentinel-1: {s1_input.shape}")
    print(f"点标签: {point_labels.shape}")
    print(f"有标签像素比例: {(point_labels != 255).float().mean():.4f}")
    
    # 阶段1训练模式
    model.train()
    results = model(s2_input, s1_input, point_labels, training=True, current_iter=100, total_iters=1000)
    
    print(f"\n阶段1输出:")
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")
    
    # 损失计算
    targets = {'point_labels': point_labels}
    total_loss, loss_dict = criterion(results, targets)
    
    print(f"\n损失计算:")
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.item():.4f}")
    
    # 测试伪标签生成
    print(f"\n测试伪标签生成:")
    model.eval()
    pseudo_labels = model.generate_pseudo_labels(s2_input, s1_input, point_labels)
    print(f"伪标签形状: {pseudo_labels.shape}")
    print(f"伪标签值范围: [{pseudo_labels.min().item()}, {pseudo_labels.max().item()}]")
    
    print("✓ CRGNet基本功能测试通过")


def test_crgnet_vs_weakly():
    """对比CRGNet与现有弱监督网络"""
    print("\n" + "=" * 60)
    print("CRGNet vs 现有弱监督网络对比")
    print("=" * 60)
    
    from models.crgnet import create_crgnet
    from models.weakly_net import create_weakly_segmentation_net
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 统一配置
    config = {
        'data': {'num_classes': 4},
        'model': {
            'fusion': {
                'fusion_type': 'mmif_cddfuse',
                's2_channels': 4,
                's1_channels': 2,
                'hidden_dim': 64,
                'num_blocks': [2, 2],
                'heads': [4, 4, 4],
                'ffn_expansion_factor': 2,
                'bias': False
            },
            'weakly_supervised': {
                'dual_classifier': True,
                'region_growing': {
                    'max_iterations': 5,
                    'confidence_threshold': 0.9,
                    'neighbor_size': 8,
                    'morphology_kernel': 3
                }
            }
        }
    }
    
    # 创建两种模型
    crgnet = create_crgnet(config).to(device)
    weakly_net = create_weakly_segmentation_net(config).to(device)
    
    # 模拟输入
    batch_size = 1
    s2_input = torch.randn(batch_size, 4, 64, 64).to(device)
    s1_input = torch.randn(batch_size, 2, 64, 64).to(device)
    point_labels = torch.randint(0, 4, (batch_size, 64, 64)).to(device)
    
    # 设置稀疏标签
    mask = torch.rand(batch_size, 64, 64) > 0.03
    point_labels[mask] = 255
    
    print(f"输入数据形状: S2{s2_input.shape}, S1{s1_input.shape}")
    print(f"有标签像素比例: {(point_labels != 255).float().mean():.4f}")
    
    # CRGNet前向传播
    with torch.no_grad():
        crgnet.eval()
        crgnet_results = crgnet(s2_input, s1_input, point_labels, training=True)
        
        # 弱监督网络前向传播
        weakly_net.eval()
        weakly_results = weakly_net(s2_input, s1_input, point_labels, training=True)
    
    # 比较结果
    print(f"\n模型比较:")
    print(f"CRGNet参数量: {sum(p.numel() for p in crgnet.parameters()):,}")
    print(f"弱监督网络参数量: {sum(p.numel() for p in weakly_net.parameters()):,}")
    
    print(f"\nCRGNet输出:")
    for key, value in crgnet_results.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
    
    print(f"\n弱监督网络输出:")
    for key, value in weakly_results.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
    
    # 比较预测差异
    if 'base_pred' in crgnet_results and 'base_pred' in weakly_results:
        crgnet_pred = F.softmax(crgnet_results['base_pred'], dim=1)
        weakly_pred = F.softmax(weakly_results['base_pred'], dim=1)
        
        pred_diff = torch.abs(crgnet_pred - weakly_pred).mean()
        print(f"\n预测差异 (平均绝对差): {pred_diff.item():.4f}")
    
    print("✓ 模型对比测试完成")


def test_region_growing():
    """测试高级区域增长算法"""
    print("\n" + "=" * 60)
    print("高级区域增长算法测试")
    print("=" * 60)
    
    from models.crgnet import AdvancedRegionGrowing
    import numpy as np
    
    # 创建区域增长算法
    region_growing = AdvancedRegionGrowing(
        confidence_threshold=0.9,
        max_iterations=5,
        morphology_kernel=3,
        boundary_removal_kernel=8
    )
    
    # 模拟数据
    batch_size, height, width = 2, 32, 32
    predictions = np.random.randint(0, 4, (batch_size, height, width))
    confidences = np.random.rand(batch_size, height, width)
    
    # 创建稀疏点标签
    point_labels = np.full((batch_size, height, width), 255, dtype=np.uint8)
    
    # 添加一些点标签
    for b in range(batch_size):
        # 随机选择一些点作为标签
        num_points = 5
        for _ in range(num_points):
            r, c = np.random.randint(0, height), np.random.randint(0, width)
            point_labels[b, r, c] = np.random.randint(0, 4)
    
    print(f"输入形状: predictions{predictions.shape}, confidences{confidences.shape}")
    print(f"初始标签点数: {(point_labels != 255).sum()}")
    
    # 执行区域增长
    expanded_labels = region_growing.grow_regions(
        predictions, confidences, point_labels, 
        current_iter=100, total_iters=1000
    )
    
    print(f"扩展后标签点数: {(expanded_labels != 255).sum()}")
    print(f"扩展比例: {(expanded_labels != 255).sum() / (point_labels != 255).sum():.2f}x")
    
    print("✓ 区域增长算法测试通过")


def main():
    """主测试函数"""
    print("CRGNet 弱监督分割网络测试")
    print("=" * 80)
    
    try:
        # 基本功能测试
        test_crgnet_basic()
        
        # 模型对比测试
        test_crgnet_vs_weakly()
        
        # 区域增长测试
        test_region_growing()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试通过！CRGNet集成成功！")
        print("=" * 80)
        
        print("\n主要特性:")
        print("✓ 基于CRGNet的双分类器架构")
        print("✓ 高级动态区域增长算法")
        print("✓ Lovász-Softmax + 一致性损失")
        print("✓ 两阶段训练流程")
        print("✓ 与MMIF-CDDFuse融合网络集成")
        print("✓ 伪标签生成和自训练支持")
        
        print("\n使用方法:")
        print("# 阶段1训练:")
        print("python train_crgnet.py --stage 1 --config config/config.yaml")
        print("\n# 阶段2训练:")
        print("python train_crgnet.py --stage 2 --stage1_model path/to/stage1/model.pth")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 