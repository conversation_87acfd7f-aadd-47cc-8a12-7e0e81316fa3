import os
import numpy as np
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from matplotlib.patches import Patch
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import cv2
from PIL import Image
import rasterio
from rasterio.transform import from_bounds
import warnings
warnings.filterwarnings('ignore')


# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class WetlandVisualizer:
    """湿地分割可视化类"""
    
    def __init__(self, class_names: List[str] = None, colors: List[str] = None):
        """
        Args:
            class_names: 类别名称列表
            colors: 颜色列表
        """
        self.class_names = class_names or ['非湿地', '湿地']
        self.colors = colors or ['#8B4513', '#0000FF']  # 棕色、蓝色
        self.num_classes = len(self.class_names)
        
        # 创建颜色映射
        self.cmap = mcolors.ListedColormap(self.colors)
    
    def visualize_predictions(self, 
                            images: Dict[str, np.ndarray],
                            predictions: np.ndarray,
                            ground_truth: np.ndarray = None,
                            confidence: np.ndarray = None,
                            save_path: str = None,
                            title: str = "湿地分割结果") -> plt.Figure:
        """
        可视化预测结果
        Args:
            images: 输入图像字典 {'sentinel2': array, 'sentinel1': array}
            predictions: 预测结果 [H, W]
            ground_truth: 真实标签 [H, W] (可选)
            confidence: 置信度 [H, W] (可选)
            save_path: 保存路径
            title: 图像标题
        Returns:
            figure: matplotlib图像对象
        """
        # 计算子图数量
        n_plots = 2  # Sentinel-2 RGB, 预测结果
        if 'sentinel1' in images:
            n_plots += 1
        if ground_truth is not None:
            n_plots += 1
        if confidence is not None:
            n_plots += 1
        
        # 创建子图
        cols = min(n_plots, 3)
        rows = (n_plots + cols - 1) // cols
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        if n_plots == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.flatten() if cols > 1 else [axes]
        else:
            axes = axes.flatten()
        
        plot_idx = 0
        
        # 1. Sentinel-2 RGB图像
        if 'sentinel2' in images:
            s2_rgb = self._create_rgb_image(images['sentinel2'])
            axes[plot_idx].imshow(s2_rgb)
            axes[plot_idx].set_title('Sentinel-2 RGB')
            axes[plot_idx].axis('off')
            plot_idx += 1
        
        # 2. Sentinel-1图像
        if 'sentinel1' in images:
            s1_composite = self._create_sar_composite(images['sentinel1'])
            axes[plot_idx].imshow(s1_composite, cmap='gray')
            axes[plot_idx].set_title('Sentinel-1 复合')
            axes[plot_idx].axis('off')
            plot_idx += 1
        
        # 3. 预测结果
        im_pred = axes[plot_idx].imshow(predictions, cmap=self.cmap, vmin=0, vmax=self.num_classes-1)
        axes[plot_idx].set_title('预测结果')
        axes[plot_idx].axis('off')
        
        # 添加图例
        legend_elements = [Patch(facecolor=self.colors[i], label=self.class_names[i]) 
                          for i in range(self.num_classes)]
        axes[plot_idx].legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
        plot_idx += 1
        
        # 4. 真实标签
        if ground_truth is not None:
            # 过滤掉ignore_index
            gt_masked = np.where(ground_truth == 255, -1, ground_truth)
            im_gt = axes[plot_idx].imshow(gt_masked, cmap=self.cmap, vmin=0, vmax=self.num_classes-1)
            axes[plot_idx].set_title('真实标签')
            axes[plot_idx].axis('off')
            plot_idx += 1
        
        # 5. 置信度图
        if confidence is not None:
            im_conf = axes[plot_idx].imshow(confidence, cmap='viridis', vmin=0, vmax=1)
            axes[plot_idx].set_title('置信度')
            axes[plot_idx].axis('off')
            plt.colorbar(im_conf, ax=axes[plot_idx], fraction=0.046, pad=0.04)
            plot_idx += 1
        
        # 隐藏多余的子图
        for i in range(plot_idx, len(axes)):
            axes[i].axis('off')
        
        plt.suptitle(title, fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
    
    def visualize_attention_maps(self,
                               attention_maps: Dict[str, np.ndarray],
                               base_image: np.ndarray = None,
                               save_path: str = None) -> plt.Figure:
        """
        可视化注意力图
        Args:
            attention_maps: 注意力图字典
            base_image: 基础图像
            save_path: 保存路径
        Returns:
            figure: matplotlib图像对象
        """
        n_maps = len(attention_maps)
        if base_image is not None:
            n_maps += 1
        
        cols = min(n_maps, 3)
        rows = (n_maps + cols - 1) // cols
        fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
        
        if n_maps == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.flatten() if cols > 1 else [axes]
        else:
            axes = axes.flatten()
        
        plot_idx = 0
        
        # 显示基础图像
        if base_image is not None:
            if base_image.ndim == 3 and base_image.shape[0] >= 3:
                rgb_image = self._create_rgb_image(base_image)
                axes[plot_idx].imshow(rgb_image)
            else:
                axes[plot_idx].imshow(base_image, cmap='gray')
            axes[plot_idx].set_title('基础图像')
            axes[plot_idx].axis('off')
            plot_idx += 1
        
        # 显示注意力图
        for name, attention in attention_maps.items():
            if attention is not None:
                im = axes[plot_idx].imshow(attention, cmap='hot', alpha=0.8)
                axes[plot_idx].set_title(f'注意力图: {name}')
                axes[plot_idx].axis('off')
                plt.colorbar(im, ax=axes[plot_idx], fraction=0.046, pad=0.04)
                plot_idx += 1
        
        # 隐藏多余的子图
        for i in range(plot_idx, len(axes)):
            axes[i].axis('off')
        
        plt.suptitle('注意力可视化', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
    
    def visualize_training_progress(self,
                                  metrics_history: Dict[str, List[float]],
                                  save_path: str = None) -> plt.Figure:
        """
        可视化训练进度
        Args:
            metrics_history: 指标历史字典
            save_path: 保存路径
        Returns:
            figure: matplotlib图像对象
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        axes = axes.flatten()
        
        # 损失曲线
        if 'train_loss' in metrics_history:
            axes[0].plot(metrics_history['train_loss'], label='训练损失', color='blue')
        if 'val_loss' in metrics_history:
            axes[0].plot(metrics_history['val_loss'], label='验证损失', color='red')
        axes[0].set_title('损失变化')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].legend()
        axes[0].grid(True)
        
        # mIoU曲线
        if 'train_miou' in metrics_history:
            axes[1].plot(metrics_history['train_miou'], label='训练mIoU', color='blue')
        if 'val_miou' in metrics_history:
            axes[1].plot(metrics_history['val_miou'], label='验证mIoU', color='red')
        axes[1].set_title('mIoU变化')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('mIoU')
        axes[1].legend()
        axes[1].grid(True)
        
        # F1-score曲线
        if 'train_f1' in metrics_history:
            axes[2].plot(metrics_history['train_f1'], label='训练F1', color='blue')
        if 'val_f1' in metrics_history:
            axes[2].plot(metrics_history['val_f1'], label='验证F1', color='red')
        axes[2].set_title('F1-score变化')
        axes[2].set_xlabel('Epoch')
        axes[2].set_ylabel('F1-score')
        axes[2].legend()
        axes[2].grid(True)
        
        # 学习率曲线
        if 'learning_rate' in metrics_history:
            axes[3].plot(metrics_history['learning_rate'], color='green')
            axes[3].set_title('学习率变化')
            axes[3].set_xlabel('Epoch')
            axes[3].set_ylabel('Learning Rate')
            axes[3].grid(True)
        else:
            axes[3].axis('off')
        
        plt.suptitle('训练进度监控', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
    
    def visualize_confusion_matrix(self,
                                 confusion_matrix: np.ndarray,
                                 save_path: str = None) -> plt.Figure:
        """
        可视化混淆矩阵
        Args:
            confusion_matrix: 混淆矩阵
            save_path: 保存路径
        Returns:
            figure: matplotlib图像对象
        """
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 归一化混淆矩阵
        cm_normalized = confusion_matrix.astype('float') / confusion_matrix.sum(axis=1)[:, np.newaxis]
        
        # 绘制热图
        sns.heatmap(cm_normalized, 
                   annot=True, 
                   fmt='.3f',
                   cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names,
                   ax=ax)
        
        ax.set_title('混淆矩阵')
        ax.set_xlabel('预测标签')
        ax.set_ylabel('真实标签')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
    
    def create_overview_figure(self,
                             sample_results: List[Dict],
                             save_path: str = None) -> plt.Figure:
        """
        创建结果总览图
        Args:
            sample_results: 样本结果列表
            save_path: 保存路径
        Returns:
            figure: matplotlib图像对象
        """
        n_samples = len(sample_results)
        fig, axes = plt.subplots(n_samples, 4, figsize=(16, 4*n_samples))
        
        if n_samples == 1:
            axes = axes.reshape(1, -1)
        
        for i, result in enumerate(sample_results):
            # Sentinel-2 RGB
            if 'sentinel2' in result:
                s2_rgb = self._create_rgb_image(result['sentinel2'])
                axes[i, 0].imshow(s2_rgb)
                axes[i, 0].set_title(f'样本 {i+1}: Sentinel-2')
                axes[i, 0].axis('off')
            
            # 真实标签
            if 'ground_truth' in result:
                gt_masked = np.where(result['ground_truth'] == 255, -1, result['ground_truth'])
                axes[i, 1].imshow(gt_masked, cmap=self.cmap, vmin=0, vmax=self.num_classes-1)
                axes[i, 1].set_title('真实标签')
                axes[i, 1].axis('off')
            
            # 预测结果
            if 'prediction' in result:
                axes[i, 2].imshow(result['prediction'], cmap=self.cmap, vmin=0, vmax=self.num_classes-1)
                axes[i, 2].set_title('预测结果')
                axes[i, 2].axis('off')
            
            # 置信度
            if 'confidence' in result:
                im = axes[i, 3].imshow(result['confidence'], cmap='viridis', vmin=0, vmax=1)
                axes[i, 3].set_title('置信度')
                axes[i, 3].axis('off')
                if i == 0:  # 只在第一行添加colorbar
                    plt.colorbar(im, ax=axes[i, 3], fraction=0.046, pad=0.04)
        
        # 添加总体图例
        legend_elements = [Patch(facecolor=self.colors[i], label=self.class_names[i]) 
                          for i in range(self.num_classes)]
        fig.legend(handles=legend_elements, loc='upper center', ncol=self.num_classes, 
                  bbox_to_anchor=(0.5, 0.98))
        
        plt.suptitle('湿地分割结果总览', fontsize=16, y=0.95)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
    
    def _create_rgb_image(self, image: np.ndarray) -> np.ndarray:
        """
        创建RGB图像
        Args:
            image: 多光谱图像 [C, H, W] 或 [H, W, C]
        Returns:
            rgb_image: RGB图像 [H, W, 3]
        """
        if image.ndim == 3:
            if image.shape[0] <= 4:  # [C, H, W]
                image = np.transpose(image, (1, 2, 0))
            
            # 选择RGB波段 (假设前3个波段是RGB)
            if image.shape[2] >= 3:
                rgb = image[:, :, :3]
            else:
                # 如果波段不足，复制第一个波段
                rgb = np.stack([image[:, :, 0]] * 3, axis=2)
        else:
            # 灰度图像
            rgb = np.stack([image] * 3, axis=2)
        
        # 归一化到0-1
        rgb = rgb.astype(np.float32)
        rgb = (rgb - rgb.min()) / (rgb.max() - rgb.min() + 1e-8)
        
        # 伽马校正
        rgb = np.power(rgb, 0.8)
        
        return np.clip(rgb, 0, 1)
    
    def _create_sar_composite(self, sar_image: np.ndarray) -> np.ndarray:
        """
        创建SAR复合图像
        Args:
            sar_image: SAR图像 [C, H, W] 或 [H, W, C]
        Returns:
            composite: 复合图像 [H, W]
        """
        if sar_image.ndim == 3:
            if sar_image.shape[0] <= 4:  # [C, H, W]
                sar_image = np.transpose(sar_image, (1, 2, 0))
            
            if sar_image.shape[2] >= 2:
                # VV和VH的比值
                composite = sar_image[:, :, 0] / (sar_image[:, :, 1] + 1e-8)
            else:
                composite = sar_image[:, :, 0]
        else:
            composite = sar_image
        
        # 对数变换和归一化
        composite = np.log(composite + 1e-8)
        composite = (composite - composite.min()) / (composite.max() - composite.min() + 1e-8)
        
        return composite


def save_prediction_as_geotiff(prediction: np.ndarray,
                             reference_file: str,
                             output_path: str,
                             patch_coords: Tuple[int, int, int, int] = None):
    """
    将预测结果保存为GeoTIFF文件
    Args:
        prediction: 预测结果 [H, W]
        reference_file: 参考地理文件
        output_path: 输出路径
        patch_coords: patch坐标 (start_row, start_col, end_row, end_col)
    """
    with rasterio.open(reference_file) as src:
        # 获取原始变换信息
        transform = src.transform
        crs = src.crs
        
        # 如果是patch，调整变换矩阵
        if patch_coords is not None:
            start_row, start_col, end_row, end_col = patch_coords
            # 计算patch的地理变换
            x_offset = start_col * transform.a
            y_offset = start_row * transform.e
            new_transform = rasterio.transform.Affine(
                transform.a, transform.b, transform.c + x_offset,
                transform.d, transform.e, transform.f + y_offset
            )
        else:
            new_transform = transform
        
        # 保存预测结果
        with rasterio.open(
            output_path,
            'w',
            driver='GTiff',
            height=prediction.shape[0],
            width=prediction.shape[1],
            count=1,
            dtype=prediction.dtype,
            crs=crs,
            transform=new_transform,
            compress='lzw'
        ) as dst:
            dst.write(prediction, 1)


def create_visualization_report(results: Dict, 
                              save_dir: str,
                              visualizer: WetlandVisualizer = None) -> str:
    """
    创建可视化报告
    Args:
        results: 结果字典
        save_dir: 保存目录
        visualizer: 可视化器
    Returns:
        report_path: 报告路径
    """
    if visualizer is None:
        visualizer = WetlandVisualizer()
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 保存各种可视化图像
    if 'predictions' in results:
        fig = visualizer.visualize_predictions(
            images=results.get('images', {}),
            predictions=results['predictions'],
            ground_truth=results.get('ground_truth'),
            confidence=results.get('confidence')
        )
        plt.savefig(os.path.join(save_dir, 'predictions.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    if 'attention_maps' in results:
        fig = visualizer.visualize_attention_maps(
            attention_maps=results['attention_maps'],
            base_image=results.get('images', {}).get('sentinel2')
        )
        plt.savefig(os.path.join(save_dir, 'attention_maps.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    if 'training_history' in results:
        fig = visualizer.visualize_training_progress(results['training_history'])
        plt.savefig(os.path.join(save_dir, 'training_progress.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    if 'confusion_matrix' in results:
        fig = visualizer.visualize_confusion_matrix(results['confusion_matrix'])
        plt.savefig(os.path.join(save_dir, 'confusion_matrix.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    return save_dir


if __name__ == "__main__":
    # 测试可视化功能
    visualizer = WetlandVisualizer()
    
    # 模拟数据
    height, width = 128, 128
    s2_image = np.random.rand(4, height, width)
    s1_image = np.random.rand(2, height, width)
    prediction = np.random.randint(0, 2, (height, width))
    ground_truth = np.random.randint(0, 2, (height, width))
    confidence = np.random.rand(height, width)
    
    # 测试预测可视化
    images = {'sentinel2': s2_image, 'sentinel1': s1_image}
    fig = visualizer.visualize_predictions(
        images=images,
        predictions=prediction,
        ground_truth=ground_truth,
        confidence=confidence,
        title="测试可视化"
    )
    plt.show()
    
    print("可视化功能测试完成") 