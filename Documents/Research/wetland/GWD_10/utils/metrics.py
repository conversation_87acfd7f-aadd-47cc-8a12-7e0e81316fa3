import numpy as np
import torch
import torch.nn.functional as F
from sklearn.metrics import confusion_matrix, precision_score, recall_score, f1_score
from typing import Dict, List, Tuple, Optional
import warnings


class SegmentationMetrics:
    """分割任务评估指标计算类"""
    
    def __init__(self, num_classes: int, ignore_index: int = 255):
        """
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的标签值
        """
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.reset()
    
    def reset(self):
        """重置所有统计量"""
        self.confusion_matrix = np.zeros((self.num_classes, self.num_classes))
        self.total_samples = 0
    
    def update(self, pred: np.ndarray, target: np.ndarray):
        """
        更新混淆矩阵
        Args:
            pred: 预测结果 [H, W] 或 [B, H, W]
            target: 真实标签 [H, W] 或 [B, H, W]
        """
        pred = pred.flatten()
        target = target.flatten()
        
        # 过滤ignore_index
        mask = target != self.ignore_index
        pred = pred[mask]
        target = target[mask]
        
        # 计算混淆矩阵
        cm = confusion_matrix(target, pred, labels=range(self.num_classes))
        self.confusion_matrix += cm
        self.total_samples += len(target)
    
    def compute_iou(self) -> Tuple[np.ndarray, float]:
        """
        计算IoU
        Returns:
            class_iou: 每个类别的IoU
            mean_iou: 平均IoU
        """
        intersection = np.diag(self.confusion_matrix)
        union = np.sum(self.confusion_matrix, axis=1) + np.sum(self.confusion_matrix, axis=0) - intersection
        
        # 避免除零
        union = np.maximum(union, 1e-10)
        class_iou = intersection / union
        
        # 只计算有样本的类别的mIoU
        valid_classes = np.sum(self.confusion_matrix, axis=1) > 0
        mean_iou = np.mean(class_iou[valid_classes]) if np.any(valid_classes) else 0.0
        
        return class_iou, mean_iou
    
    def compute_dice(self) -> Tuple[np.ndarray, float]:
        """
        计算Dice系数
        Returns:
            class_dice: 每个类别的Dice系数
            mean_dice: 平均Dice系数
        """
        intersection = np.diag(self.confusion_matrix)
        dice_denominator = np.sum(self.confusion_matrix, axis=1) + np.sum(self.confusion_matrix, axis=0)
        
        # 避免除零
        dice_denominator = np.maximum(dice_denominator, 1e-10)
        class_dice = 2 * intersection / dice_denominator
        
        # 只计算有样本的类别的平均Dice
        valid_classes = np.sum(self.confusion_matrix, axis=1) > 0
        mean_dice = np.mean(class_dice[valid_classes]) if np.any(valid_classes) else 0.0
        
        return class_dice, mean_dice
    
    def compute_precision_recall_f1(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, float, float, float]:
        """
        计算Precision, Recall, F1-score
        Returns:
            precision: 每个类别的精确率
            recall: 每个类别的召回率  
            f1: 每个类别的F1分数
            mean_precision: 平均精确率
            mean_recall: 平均召回率
            mean_f1: 平均F1分数
        """
        # 计算每个类别的TP, FP, FN
        tp = np.diag(self.confusion_matrix)
        fp = np.sum(self.confusion_matrix, axis=0) - tp
        fn = np.sum(self.confusion_matrix, axis=1) - tp
        
        # 计算precision, recall, f1
        precision = tp / np.maximum(tp + fp, 1e-10)
        recall = tp / np.maximum(tp + fn, 1e-10)
        f1 = 2 * precision * recall / np.maximum(precision + recall, 1e-10)
        
        # 只计算有样本的类别的平均值
        valid_classes = np.sum(self.confusion_matrix, axis=1) > 0
        mean_precision = np.mean(precision[valid_classes]) if np.any(valid_classes) else 0.0
        mean_recall = np.mean(recall[valid_classes]) if np.any(valid_classes) else 0.0
        mean_f1 = np.mean(f1[valid_classes]) if np.any(valid_classes) else 0.0
        
        return precision, recall, f1, mean_precision, mean_recall, mean_f1
    
    def compute_overall_accuracy(self) -> float:
        """计算总体精度"""
        if self.total_samples == 0:
            return 0.0
        return np.trace(self.confusion_matrix) / np.sum(self.confusion_matrix)
    
    def compute_kappa(self) -> float:
        """计算Kappa系数"""
        if self.total_samples == 0:
            return 0.0
            
        cm = self.confusion_matrix
        n = np.sum(cm)
        po = np.trace(cm) / n  # 观测一致性
        
        # 期望一致性
        pe = np.sum(np.sum(cm, axis=1) * np.sum(cm, axis=0)) / (n * n)
        
        if pe == 1:
            return 0.0
        
        kappa = (po - pe) / (1 - pe)
        return kappa
    
    def get_all_metrics(self) -> Dict[str, float]:
        """获取所有评估指标"""
        class_iou, mean_iou = self.compute_iou()
        class_dice, mean_dice = self.compute_dice()
        precision, recall, f1, mean_precision, mean_recall, mean_f1 = self.compute_precision_recall_f1()
        oa = self.compute_overall_accuracy()
        kappa = self.compute_kappa()
        
        metrics = {
            'mIoU': mean_iou,
            'mDice': mean_dice,
            'mPrecision': mean_precision,
            'mRecall': mean_recall,
            'mF1': mean_f1,
            'OA': oa,
            'Kappa': kappa
        }
        
        # 添加每个类别的指标
        for i in range(self.num_classes):
            metrics[f'IoU_class_{i}'] = class_iou[i]
            metrics[f'Dice_class_{i}'] = class_dice[i]
            metrics[f'Precision_class_{i}'] = precision[i]
            metrics[f'Recall_class_{i}'] = recall[i]
            metrics[f'F1_class_{i}'] = f1[i]
        
        return metrics
    
    def print_metrics(self, class_names: Optional[List[str]] = None):
        """打印评估指标"""
        if class_names is None:
            class_names = [f'Class_{i}' for i in range(self.num_classes)]
        
        class_iou, mean_iou = self.compute_iou()
        class_dice, mean_dice = self.compute_dice()
        precision, recall, f1, mean_precision, mean_recall, mean_f1 = self.compute_precision_recall_f1()
        oa = self.compute_overall_accuracy()
        kappa = self.compute_kappa()
        
        print("=" * 60)
        print("分割评估结果")
        print("=" * 60)
        
        # 整体指标
        print(f"总体精度 (OA): {oa:.4f}")
        print(f"平均IoU (mIoU): {mean_iou:.4f}")
        print(f"平均Dice (mDice): {mean_dice:.4f}")
        print(f"平均F1-score (mF1): {mean_f1:.4f}")
        print(f"平均Precision (mPrecision): {mean_precision:.4f}")
        print(f"平均Recall (mRecall): {mean_recall:.4f}")
        print(f"Kappa系数: {kappa:.4f}")
        print()
        
        # 各类别指标
        print("各类别详细指标:")
        print("-" * 80)
        print(f"{'类别':<15} {'IoU':<8} {'Dice':<8} {'Precision':<10} {'Recall':<8} {'F1-score':<10}")
        print("-" * 80)
        
        for i in range(self.num_classes):
            if i < len(class_names):
                class_name = class_names[i]
            else:
                class_name = f'Class_{i}'
            
            print(f"{class_name:<15} {class_iou[i]:<8.4f} {class_dice[i]:<8.4f} "
                  f"{precision[i]:<10.4f} {recall[i]:<8.4f} {f1[i]:<10.4f}")
        
        print("-" * 80)


def evaluate_model(model, data_loader, device, num_classes: int, class_names: Optional[List[str]] = None) -> Dict[str, float]:
    """
    评估模型性能
    Args:
        model: 要评估的模型
        data_loader: 数据加载器
        device: 设备
        num_classes: 类别数量
        class_names: 类别名称列表
    Returns:
        metrics: 评估指标字典
    """
    model.eval()
    metrics_calculator = SegmentationMetrics(num_classes)
    
    with torch.no_grad():
        for batch in data_loader:
            # 获取数据
            s2_data = batch['sentinel2'].to(device)
            s1_data = batch['sentinel1'].to(device)
            targets = batch['point_labels'].to(device)
            
            # 前向传播
            results = model(s2_data, s1_data, training=False)
            
            # 获取预测结果
            if 'expanded_pred' in results:
                # 如果有扩展预测，使用基础预测和扩展预测的平均
                base_pred = results['base_pred']
                expanded_pred = results['expanded_pred']
                predictions = F.softmax(base_pred, dim=1) + F.softmax(expanded_pred, dim=1)
                predictions = torch.argmax(predictions, dim=1)
            else:
                predictions = torch.argmax(results['base_pred'], dim=1)
            
            # 转换为numpy
            predictions_np = predictions.cpu().numpy()
            targets_np = targets.cpu().numpy()
            
            # 更新指标
            metrics_calculator.update(predictions_np, targets_np)
    
    # 计算所有指标
    metrics = metrics_calculator.get_all_metrics()
    
    # 打印指标
    if class_names is None:
        class_names = ['非湿地', '湿地'] if num_classes == 2 else [f'类别_{i}' for i in range(num_classes)]
    
    metrics_calculator.print_metrics(class_names)
    
    return metrics


def compute_batch_metrics(pred: torch.Tensor, target: torch.Tensor, num_classes: int) -> Dict[str, float]:
    """
    计算单个batch的评估指标
    Args:
        pred: 预测结果 [B, C, H, W]
        target: 真实标签 [B, H, W]
        num_classes: 类别数量
    Returns:
        metrics: 评估指标字典
    """
    pred_labels = torch.argmax(pred, dim=1)
    
    # 转换为numpy
    pred_np = pred_labels.cpu().numpy()
    target_np = target.cpu().numpy()
    
    # 计算指标
    metrics_calculator = SegmentationMetrics(num_classes)
    metrics_calculator.update(pred_np, target_np)
    
    return metrics_calculator.get_all_metrics()


class RunningMetrics:
    """运行时指标计算器"""
    
    def __init__(self, num_classes: int):
        self.num_classes = num_classes
        self.metrics_calculator = SegmentationMetrics(num_classes)
        self.batch_count = 0
    
    def update(self, pred: torch.Tensor, target: torch.Tensor):
        """更新指标"""
        if pred.dim() == 4:  # [B, C, H, W]
            pred = torch.argmax(pred, dim=1)
        
        pred_np = pred.cpu().numpy()
        target_np = target.cpu().numpy()
        
        self.metrics_calculator.update(pred_np, target_np)
        self.batch_count += 1
    
    def get_metrics(self) -> Dict[str, float]:
        """获取当前指标"""
        return self.metrics_calculator.get_all_metrics()
    
    def reset(self):
        """重置指标"""
        self.metrics_calculator.reset()
        self.batch_count = 0


if __name__ == "__main__":
    # 测试评估指标
    np.random.seed(42)
    
    # 模拟数据
    num_classes = 2
    batch_size = 4
    height, width = 128, 128
    
    # 模拟预测和真实标签
    pred = torch.randn(batch_size, num_classes, height, width)
    target = torch.randint(0, num_classes, (batch_size, height, width))
    
    # 计算指标
    metrics = compute_batch_metrics(pred, target, num_classes)
    
    print("单个batch评估指标:")
    for key, value in metrics.items():
        print(f"{key}: {value:.4f}")
    
    # 测试运行时指标计算器
    running_metrics = RunningMetrics(num_classes)
    
    # 模拟多个batch
    for i in range(5):
        pred = torch.randn(batch_size, num_classes, height, width)
        target = torch.randint(0, num_classes, (batch_size, height, width))
        running_metrics.update(pred, target)
    
    print("\n累积指标:")
    final_metrics = running_metrics.get_metrics()
    for key, value in final_metrics.items():
        if not key.startswith('class'):  # 只显示主要指标
            print(f"{key}: {value:.4f}") 