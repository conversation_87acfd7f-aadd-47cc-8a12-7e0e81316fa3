import os
import argparse
import yaml
import torch
import torch.nn.functional as F
import numpy as np
from tqdm import tqdm
import json
import time
from datetime import datetime

# 导入自定义模块
from models.weakly_net import create_weakly_segmentation_net
from data.dataset import create_data_loaders
from utils.metrics import SegmentationMetrics, evaluate_model
from utils.visualization import WetlandVisualizer, save_prediction_as_geotiff


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='湿地遥感影像分割测试')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                        help='配置文件路径')
    parser.add_argument('--model_path', type=str, required=True,
                        help='训练好的模型路径')
    parser.add_argument('--output_dir', type=str, default='test_results/',
                        help='测试结果输出目录')
    parser.add_argument('--save_predictions', action='store_true',
                        help='是否保存预测结果')
    parser.add_argument('--save_visualizations', action='store_true',
                        help='是否保存可视化结果')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='使用的GPU ID')
    return parser.parse_args()


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def load_model(model_path, config, device):
    """加载训练好的模型"""
    model = create_weakly_segmentation_net(config)
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location='cpu')
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    print(f"模型已加载: {model_path}")
    return model


def test_model(model, test_loader, device, config, save_predictions=False, save_visualizations=False, output_dir=None):
    """测试模型"""
    model.eval()
    
    # 初始化指标计算器
    metrics_calculator = SegmentationMetrics(config['data']['num_classes'])
    
    # 可视化器
    visualizer = WetlandVisualizer(['非湿地', '湿地'])
    
    # 存储结果
    all_predictions = []
    all_targets = []
    all_confidences = []
    sample_results = []
    
    print("开始测试...")
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(test_loader, desc='测试中')):
            # 获取数据
            s2_data = batch['sentinel2'].to(device)
            s1_data = batch['sentinel1'].to(device)
            targets = batch['point_labels'].to(device)
            image_names = batch['image_name']
            patch_coords = batch['patch_coords']
            
            # 前向传播
            results = model(s2_data, s1_data, training=False)
            
            # 获取预测结果和置信度
            if 'expanded_pred' in results:
                # 融合基础预测和扩展预测
                base_prob = F.softmax(results['base_pred'], dim=1)
                expanded_prob = F.softmax(results['expanded_pred'], dim=1)
                final_prob = (base_prob + expanded_prob) / 2.0
            else:
                final_prob = F.softmax(results['base_pred'], dim=1)
            
            # 获取预测类别和置信度
            confidence, predictions = torch.max(final_prob, dim=1)
            
            # 转换为numpy
            predictions_np = predictions.cpu().numpy()
            targets_np = targets.cpu().numpy()
            confidence_np = confidence.cpu().numpy()
            s2_np = s2_data.cpu().numpy()
            s1_np = s1_data.cpu().numpy()
            
            # 更新指标
            metrics_calculator.update(predictions_np, targets_np)
            
            # 保存结果
            all_predictions.extend(predictions_np)
            all_targets.extend(targets_np)
            all_confidences.extend(confidence_np)
            
            # 保存样本结果用于可视化
            for i in range(predictions_np.shape[0]):
                sample_result = {
                    'image_name': image_names[i],
                    'patch_coords': patch_coords[i],
                    'prediction': predictions_np[i],
                    'ground_truth': targets_np[i],
                    'confidence': confidence_np[i],
                    'sentinel2': s2_np[i],
                    'sentinel1': s1_np[i]
                }
                sample_results.append(sample_result)
                
                # 保存预测结果为GeoTIFF
                if save_predictions and output_dir:
                    pred_dir = os.path.join(output_dir, 'predictions')
                    os.makedirs(pred_dir, exist_ok=True)
                    
                    # 假设存在参考文件
                    reference_file = os.path.join(config['data']['sentinel2_dir'], f"{image_names[i]}_S2.tif")
                    if os.path.exists(reference_file):
                        output_path = os.path.join(pred_dir, f"{image_names[i]}_prediction.tif")
                        save_prediction_as_geotiff(
                            predictions_np[i],
                            reference_file,
                            output_path,
                            patch_coords[i]
                        )
            
            # 保存可视化结果
            if save_visualizations and output_dir and batch_idx < 10:  # 只保存前10个batch
                vis_dir = os.path.join(output_dir, 'visualizations')
                os.makedirs(vis_dir, exist_ok=True)
                
                for i in range(min(predictions_np.shape[0], 3)):  # 每个batch最多保存3个样本
                    images = {
                        'sentinel2': s2_np[i],
                        'sentinel1': s1_np[i]
                    }
                    
                    fig = visualizer.visualize_predictions(
                        images=images,
                        predictions=predictions_np[i],
                        ground_truth=targets_np[i],
                        confidence=confidence_np[i],
                        title=f"测试样本: {image_names[i]}"
                    )
                    
                    save_path = os.path.join(vis_dir, f"sample_{batch_idx}_{i}.png")
                    fig.savefig(save_path, dpi=300, bbox_inches='tight')
                    plt.close()
    
    # 计算最终指标
    final_metrics = metrics_calculator.get_all_metrics()
    
    # 打印详细结果
    print("\n" + "="*60)
    print("测试结果")
    print("="*60)
    
    metrics_calculator.print_metrics(['非湿地', '湿地'])
    
    # 生成混淆矩阵可视化
    if save_visualizations and output_dir:
        vis_dir = os.path.join(output_dir, 'visualizations')
        confusion_fig = visualizer.visualize_confusion_matrix(metrics_calculator.confusion_matrix)
        confusion_fig.savefig(os.path.join(vis_dir, 'confusion_matrix.png'), 
                             dpi=300, bbox_inches='tight')
        plt.close()
        
        # 生成样本总览图
        if len(sample_results) > 0:
            # 选择一些代表性样本
            selected_samples = sample_results[:min(6, len(sample_results))]
            overview_fig = visualizer.create_overview_figure(selected_samples)
            overview_fig.savefig(os.path.join(vis_dir, 'overview.png'), 
                                dpi=300, bbox_inches='tight')
            plt.close()
    
    return final_metrics, sample_results


def generate_test_report(metrics, sample_results, config, output_dir):
    """生成测试报告"""
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'model_config': {
            'num_classes': config['data']['num_classes'],
            'fusion_type': config['model']['fusion']['fusion_type'],
            'use_dual_classifier': config['model']['weakly_supervised']['dual_classifier']
        },
        'test_metrics': metrics,
        'test_statistics': {
            'total_samples': len(sample_results),
            'avg_confidence': np.mean([s['confidence'].mean() for s in sample_results]),
            'std_confidence': np.std([s['confidence'].mean() for s in sample_results])
        }
    }
    
    # 保存报告
    report_path = os.path.join(output_dir, 'test_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    md_report_path = os.path.join(output_dir, 'test_report.md')
    with open(md_report_path, 'w', encoding='utf-8') as f:
        f.write("# 湿地遥感影像分割测试报告\n\n")
        f.write(f"**测试时间**: {report['test_timestamp']}\n\n")
        f.write(f"**测试样本数**: {report['test_statistics']['total_samples']}\n\n")
        
        f.write("## 模型配置\n\n")
        f.write(f"- 类别数量: {report['model_config']['num_classes']}\n")
        f.write(f"- 融合类型: {report['model_config']['fusion_type']}\n")
        f.write(f"- 双分类器: {report['model_config']['use_dual_classifier']}\n\n")
        
        f.write("## 测试指标\n\n")
        f.write(f"- **mIoU**: {metrics['mIoU']:.4f}\n")
        f.write(f"- **mF1**: {metrics['mF1']:.4f}\n")
        f.write(f"- **总体精度 (OA)**: {metrics['OA']:.4f}\n")
        f.write(f"- **Kappa系数**: {metrics['Kappa']:.4f}\n\n")
        
        f.write("### 各类别详细指标\n\n")
        class_names = ['非湿地', '湿地']
        f.write("| 类别 | IoU | F1-score | Precision | Recall |\n")
        f.write("|------|-----|----------|-----------|--------|\n")
        
        for i in range(len(class_names)):
            iou = metrics[f'IoU_class_{i}']
            f1 = metrics[f'F1_class_{i}']
            precision = metrics[f'Precision_class_{i}']
            recall = metrics[f'Recall_class_{i}']
            f.write(f"| {class_names[i]} | {iou:.4f} | {f1:.4f} | {precision:.4f} | {recall:.4f} |\n")
        
        f.write(f"\n## 置信度统计\n\n")
        f.write(f"- **平均置信度**: {report['test_statistics']['avg_confidence']:.4f}\n")
        f.write(f"- **置信度标准差**: {report['test_statistics']['std_confidence']:.4f}\n")
    
    print(f"\n测试报告已保存到: {report_path}")
    print(f"Markdown报告已保存到: {md_report_path}")


def main():
    # 解析参数
    args = parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置设备
    device = torch.device(f'cuda:{args.gpu_id}' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(args.output_dir, f"test_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    print("=" * 60)
    print("湿地遥感影像分割测试")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"输出目录: {output_dir}")
    
    # 创建数据加载器
    print("\n创建数据加载器...")
    try:
        _, test_loader = create_data_loaders(config)
        print(f"测试样本数: {len(test_loader.dataset)}")
    except Exception as e:
        print(f"数据加载失败: {e}")
        print("请检查数据路径和配置")
        return
    
    # 加载模型
    print("\n加载模型...")
    model = load_model(args.model_path, config, device)
    
    # 进行测试
    print("\n开始测试...")
    start_time = time.time()
    
    metrics, sample_results = test_model(
        model, test_loader, device, config,
        save_predictions=args.save_predictions,
        save_visualizations=args.save_visualizations,
        output_dir=output_dir
    )
    
    test_time = time.time() - start_time
    print(f"\n测试完成，耗时: {test_time:.2f}秒")
    
    # 生成测试报告
    print("\n生成测试报告...")
    generate_test_report(metrics, sample_results, config, output_dir)
    
    print(f"\n所有测试结果已保存到: {output_dir}")


if __name__ == '__main__':
    import matplotlib.pyplot as plt
    main() 