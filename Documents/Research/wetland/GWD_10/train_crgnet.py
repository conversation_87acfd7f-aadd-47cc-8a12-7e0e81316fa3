#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CRGNet两阶段训练脚本
阶段1: 基于点标签的区域增长训练
阶段2: 基于伪标签的自训练
"""

import os
import argparse
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import time
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from models.crgnet import create_crgnet, create_crgnet_loss
from data.dataset import create_data_loaders
from utils.metrics import RunningMetrics, evaluate_model
from utils.visualization import WetlandVisualizer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='CRGNet湿地遥感影像分割训练')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                        help='配置文件路径')
    parser.add_argument('--stage', type=int, default=1,
                        help='训练阶段: 1-区域增长训练, 2-自训练')
    parser.add_argument('--resume', type=str, default=None,
                        help='恢复训练的检查点路径')
    parser.add_argument('--stage1_model', type=str, default=None,
                        help='阶段1模型路径（用于阶段2）')
    parser.add_argument('--gpu_ids', type=str, default='0',
                        help='使用的GPU ID，用逗号分隔')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def setup_device(gpu_ids):
    """设置设备"""
    if torch.cuda.is_available() and gpu_ids:
        gpu_list = [int(x) for x in gpu_ids.split(',')]
        device = torch.device(f'cuda:{gpu_list[0]}')
        if len(gpu_list) > 1:
            print(f"使用多GPU训练: {gpu_list}")
        else:
            print(f"使用GPU: {gpu_list[0]}")
    else:
        device = torch.device('cpu')
        print("使用CPU训练")
    return device, gpu_list if 'gpu_list' in locals() else []


def create_optimizer(model, config):
    """创建优化器"""
    training_config = config['training']
    
    optimizer = optim.SGD(
        model.parameters(),
        lr=training_config['learning_rate'],
        momentum=training_config['momentum'],
        weight_decay=training_config['weight_decay']
    )
    
    return optimizer


def create_scheduler(optimizer, config):
    """创建学习率调度器"""
    scheduler_config = config['training']['scheduler']
    
    if scheduler_config['type'] == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=config['training']['num_epochs'],
            eta_min=scheduler_config['min_lr']
        )
    elif scheduler_config['type'] == 'step':
        scheduler = optim.lr_scheduler.StepLR(
            optimizer,
            step_size=scheduler_config.get('step_size', 30),
            gamma=scheduler_config.get('gamma', 0.1)
        )
    else:
        scheduler = None
    
    return scheduler


def adjust_learning_rate(optimizer, base_lr, current_iter, total_iters):
    """动态调整学习率"""
    lr = base_lr * (1 - float(current_iter) / total_iters) ** 0.9
    for param_group in optimizer.param_groups:
        param_group['lr'] = lr


def train_stage1_epoch(model, train_loader, criterion, optimizer, device, config, epoch, total_epochs):
    """训练阶段1的一个epoch - 区域增长训练"""
    model.train()
    running_loss = 0.0
    running_metrics = RunningMetrics(config['data']['num_classes'])
    
    progress_bar = tqdm(train_loader, desc=f'阶段1-Epoch {epoch+1}')
    
    for batch_idx, batch in enumerate(progress_bar):
        # 获取数据
        s2_data = batch['sentinel2'].to(device)
        s1_data = batch['sentinel1'].to(device)
        point_labels = batch['point_labels'].to(device)
        
        # 动态调整学习率
        current_iter = epoch * len(train_loader) + batch_idx
        total_iters = total_epochs * len(train_loader)
        adjust_learning_rate(optimizer, config['training']['learning_rate'], current_iter, total_iters)
        
        # 前向传播（带区域增长）
        optimizer.zero_grad()
        results = model(s2_data, s1_data, point_labels, training=True, 
                       current_iter=current_iter, total_iters=total_iters)
        
        # 计算损失
        targets = {'point_labels': point_labels}
        total_loss, loss_dict = criterion(results, targets)
        
        # 反向传播
        total_loss.backward()
        optimizer.step()
        
        # 更新统计
        running_loss += total_loss.item()
        
        # 更新指标
        running_metrics.update(results['base_pred'], point_labels)
        
        # 更新进度条
        current_lr = optimizer.param_groups[0]['lr']
        progress_bar.set_postfix({
            'Loss': f'{total_loss.item():.4f}',
            'Seg': f'{loss_dict["segmentation_loss"].item():.4f}',
            'Exp': f'{loss_dict["expansion_loss"].item():.4f}',
            'Con': f'{loss_dict["consistency_loss"].item():.4f}',
            'LR': f'{current_lr:.6f}'
        })
    
    # 计算平均损失和指标
    avg_loss = running_loss / len(train_loader)
    metrics = running_metrics.get_metrics()
    
    return avg_loss, metrics, loss_dict


def generate_pseudo_labels(model, train_loader, device, output_dir):
    """生成伪标签用于阶段2训练"""
    print("生成伪标签...")
    model.eval()
    
    pseudo_labels_dir = os.path.join(output_dir, 'pseudo_labels')
    os.makedirs(pseudo_labels_dir, exist_ok=True)
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(train_loader, desc='生成伪标签')):
            s2_data = batch['sentinel2'].to(device)
            s1_data = batch['sentinel1'].to(device)
            point_labels = batch['point_labels'].to(device)
            
            # 生成伪标签
            pseudo_labels = model.generate_pseudo_labels(s2_data, s1_data, point_labels)
            
            # 保存伪标签（这里简化处理，实际应用中需要保存到文件）
            # 在实际实现中，可以保存为.png文件或其他格式
            pass
    
    print(f"伪标签已保存到: {pseudo_labels_dir}")


def train_stage2_epoch(model, train_loader, criterion, optimizer, device, config, epoch):
    """训练阶段2的一个epoch - 自训练"""
    model.train()
    running_loss = 0.0
    running_metrics = RunningMetrics(config['data']['num_classes'])
    
    progress_bar = tqdm(train_loader, desc=f'阶段2-Epoch {epoch+1}')
    
    for batch_idx, batch in enumerate(progress_bar):
        # 获取数据（包含伪标签）
        s2_data = batch['sentinel2'].to(device)
        s1_data = batch['sentinel1'].to(device)
        point_labels = batch['point_labels'].to(device)
        
        # 这里应该加载预生成的伪标签，为简化起见，我们动态生成
        with torch.no_grad():
            pseudo_labels = model.generate_pseudo_labels(s2_data, s1_data, point_labels)
        
        # 前向传播
        optimizer.zero_grad()
        results = model(s2_data, s1_data, point_labels, training=False)
        
        # 添加伪标签到结果中
        results['expanded_labels'] = pseudo_labels
        
        # 计算损失
        targets = {'point_labels': point_labels}
        total_loss, loss_dict = criterion(results, targets)
        
        # 反向传播
        total_loss.backward()
        optimizer.step()
        
        # 更新统计
        running_loss += total_loss.item()
        running_metrics.update(results['base_pred'], point_labels)
        
        # 更新进度条
        current_lr = optimizer.param_groups[0]['lr']
        progress_bar.set_postfix({
            'Loss': f'{total_loss.item():.4f}',
            'Seg': f'{loss_dict["segmentation_loss"].item():.4f}',
            'Exp': f'{loss_dict["expansion_loss"].item():.4f}',
            'Con': f'{loss_dict["consistency_loss"].item():.4f}',
            'LR': f'{current_lr:.6f}'
        })
    
    # 计算平均损失和指标
    avg_loss = running_loss / len(train_loader)
    metrics = running_metrics.get_metrics()
    
    return avg_loss, metrics, loss_dict


def validate_epoch(model, val_loader, criterion, device, config):
    """验证一个epoch"""
    model.eval()
    running_loss = 0.0
    running_metrics = RunningMetrics(config['data']['num_classes'])
    
    with torch.no_grad():
        progress_bar = tqdm(val_loader, desc='验证中')
        
        for batch in progress_bar:
            # 获取数据
            s2_data = batch['sentinel2'].to(device)
            s1_data = batch['sentinel1'].to(device)
            point_labels = batch['point_labels'].to(device)
            
            # 前向传播
            results = model(s2_data, s1_data, training=False)
            
            # 计算损失（仅使用基础分类器）
            targets = {'point_labels': point_labels}
            total_loss, loss_dict = criterion(results, targets)
            
            # 更新统计
            running_loss += total_loss.item()
            running_metrics.update(results['base_pred'], point_labels)
            
            progress_bar.set_postfix({'Loss': f'{total_loss.item():.4f}'})
    
    # 计算平均损失和指标
    avg_loss = running_loss / len(val_loader)
    metrics = running_metrics.get_metrics()
    
    return avg_loss, metrics


def save_checkpoint(model, optimizer, scheduler, epoch, metrics, save_path, stage):
    """保存检查点"""
    checkpoint = {
        'stage': stage,
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'metrics': metrics,
        'timestamp': datetime.now().isoformat()
    }
    
    torch.save(checkpoint, save_path)
    print(f"检查点已保存: {save_path}")


def load_checkpoint(checkpoint_path, model, optimizer=None, scheduler=None):
    """加载检查点"""
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    model.load_state_dict(checkpoint['model_state_dict'])
    
    if optimizer and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    if scheduler and checkpoint.get('scheduler_state_dict'):
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    stage = checkpoint.get('stage', 1)
    epoch = checkpoint.get('epoch', 0)
    metrics = checkpoint.get('metrics', {})
    
    print(f"检查点已加载: {checkpoint_path}, 阶段: {stage}, Epoch: {epoch}")
    return stage, epoch, metrics


def main():
    # 解析参数
    args = parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置设备
    device, gpu_list = setup_device(args.gpu_ids)
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    experiment_name = f"crgnet_stage{args.stage}_{timestamp}"
    output_dir = os.path.join(config['paths']['checkpoint_dir'], experiment_name)
    log_dir = os.path.join(config['paths']['log_dir'], experiment_name)
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # 保存配置文件
    with open(os.path.join(output_dir, 'config.yaml'), 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 创建tensorboard日志
    writer = SummaryWriter(log_dir)
    
    print("=" * 80)
    print(f"CRGNet湿地遥感影像分割训练 - 阶段{args.stage}")
    print("=" * 80)
    print(f"实验名称: {experiment_name}")
    print(f"输出目录: {output_dir}")
    print(f"设备: {device}")
    print(f"随机种子: {args.seed}")
    
    # 创建数据加载器
    print("\n创建数据加载器...")
    try:
        train_loader, val_loader = create_data_loaders(config)
        print(f"训练样本数: {len(train_loader.dataset)}")
        print(f"验证样本数: {len(val_loader.dataset)}")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 创建模型
    print("\n创建CRGNet模型...")
    model = create_crgnet(config)
    
    # 多GPU支持
    if len(gpu_list) > 1:
        model = nn.DataParallel(model, device_ids=gpu_list)
    
    model = model.to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
    
    # 创建损失函数
    criterion = create_crgnet_loss(config)
    
    # 创建优化器和调度器
    optimizer = create_optimizer(model, config)
    scheduler = create_scheduler(optimizer, config)
    
    # 加载阶段1模型（如果是阶段2训练）
    start_epoch = 0
    if args.stage == 2 and args.stage1_model:
        print(f"\n加载阶段1模型: {args.stage1_model}")
        checkpoint = torch.load(args.stage1_model, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        print("阶段1模型加载成功")
    
    # 恢复训练
    if args.resume:
        stage, start_epoch, _ = load_checkpoint(args.resume, model, optimizer, scheduler)
        if stage != args.stage:
            print(f"警告: 检查点阶段({stage})与指定阶段({args.stage})不匹配")
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_miou': [],
        'val_miou': [],
        'train_f1': [],
        'val_f1': [],
        'learning_rate': []
    }
    
    # 最佳指标跟踪
    best_miou = 0.0
    best_f1 = 0.0
    patience_counter = 0
    
    print(f"\n开始阶段{args.stage}训练...")
    print("-" * 80)
    
    # 训练循环
    for epoch in range(start_epoch, config['training']['num_epochs']):
        epoch_start_time = time.time()
        
        print(f"\nEpoch {epoch+1}/{config['training']['num_epochs']}")
        
        # 根据阶段选择训练函数
        if args.stage == 1:
            train_loss, train_metrics, _ = train_stage1_epoch(
                model, train_loader, criterion, optimizer, device, config, 
                epoch, config['training']['num_epochs']
            )
        else:
            train_loss, train_metrics, _ = train_stage2_epoch(
                model, train_loader, criterion, optimizer, device, config, epoch
            )
        
        # 验证
        val_loss, val_metrics = validate_epoch(
            model, val_loader, criterion, device, config
        )
        
        # 更新学习率
        if scheduler:
            if isinstance(scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                scheduler.step(val_metrics['mIoU'])
            else:
                scheduler.step()
        
        # 记录指标
        current_lr = optimizer.param_groups[0]['lr']
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_miou'].append(train_metrics['mIoU'])
        history['val_miou'].append(val_metrics['mIoU'])
        history['train_f1'].append(train_metrics['mF1'])
        history['val_f1'].append(val_metrics['mF1'])
        history['learning_rate'].append(current_lr)
        
        # 记录到tensorboard
        writer.add_scalar('Loss/Train', train_loss, epoch)
        writer.add_scalar('Loss/Val', val_loss, epoch)
        writer.add_scalar('Metrics/Train_mIoU', train_metrics['mIoU'], epoch)
        writer.add_scalar('Metrics/Val_mIoU', val_metrics['mIoU'], epoch)
        writer.add_scalar('Metrics/Train_F1', train_metrics['mF1'], epoch)
        writer.add_scalar('Metrics/Val_F1', val_metrics['mF1'], epoch)
        writer.add_scalar('Learning_Rate', current_lr, epoch)
        
        # 打印结果
        epoch_time = time.time() - epoch_start_time
        print(f"训练损失: {train_loss:.4f} | 验证损失: {val_loss:.4f}")
        print(f"训练mIoU: {train_metrics['mIoU']:.4f} | 验证mIoU: {val_metrics['mIoU']:.4f}")
        print(f"训练F1: {train_metrics['mF1']:.4f} | 验证F1: {val_metrics['mF1']:.4f}")
        print(f"学习率: {current_lr:.6f} | 耗时: {epoch_time:.2f}s")
        
        # 保存最佳模型
        if val_metrics['mIoU'] > best_miou:
            best_miou = val_metrics['mIoU']
            patience_counter = 0
            
            # 保存最佳模型
            best_model_path = os.path.join(output_dir, f'best_stage{args.stage}_model.pth')
            save_checkpoint(model, optimizer, scheduler, epoch, val_metrics, best_model_path, args.stage)
            print(f"新的最佳mIoU: {best_miou:.4f}")
            
        else:
            patience_counter += 1
        
        # 定期保存检查点
        if (epoch + 1) % config['training']['save_frequency'] == 0:
            checkpoint_path = os.path.join(output_dir, f'checkpoint_stage{args.stage}_epoch_{epoch+1}.pth')
            save_checkpoint(model, optimizer, scheduler, epoch, val_metrics, checkpoint_path, args.stage)
        
        # 早停检查
        if patience_counter >= config['training']['early_stopping_patience']:
            print(f"\n早停触发，连续{patience_counter}个epoch无改进")
            break
    
    print(f"\n阶段{args.stage}训练完成!")
    print(f"最佳mIoU: {best_miou:.4f}")
    
    # 生成伪标签（仅在阶段1结束后）
    if args.stage == 1:
        best_model_path = os.path.join(output_dir, f'best_stage{args.stage}_model.pth')
        checkpoint = torch.load(best_model_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        generate_pseudo_labels(model, train_loader, device, output_dir)
        print(f"\n阶段1完成，请使用以下模型进行阶段2训练:")
        print(f"python train_crgnet.py --stage 2 --stage1_model {best_model_path}")
    
    # 保存训练历史
    history_path = os.path.join(output_dir, 'training_history.json')
    with open(history_path, 'w') as f:
        json.dump(history, f, indent=2)
    
    print(f"\n实验结果已保存到: {output_dir}")
    writer.close()


if __name__ == '__main__':
    main() 