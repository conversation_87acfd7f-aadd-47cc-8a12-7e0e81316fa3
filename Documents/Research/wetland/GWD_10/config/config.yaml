# 湿地遥感影像分割配置文件

# 数据配置
data:
  # 数据路径
  sentinel2_dir: "data/sentinel2/"         # Sentinel-2 数据目录
  sentinel1_dir: "data/sentinel1/"         # Sentinel-1 数据目录
  point_labels_path: "data/point_labels.shp"  # 点样本标注文件
  train_list: "data/train_list.txt"        # 训练数据列表
  test_list: "data/test_list.txt"          # 测试数据列表
  
  # 数据参数
  input_size: [128, 128]                   # 输入图像尺寸
  num_classes: 4                           # 类别数量 (水体/草本沼泽/木本沼泽/泥滩)
  s2_bands: [1, 2, 3, 7]                  # Sentinel-2 使用的波段 (B2,B3,B4,B8)
  s1_bands: [0, 1]                         # Sentinel-1 使用的极化 (VV,VH)
  
  # 数据增强
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.5
    rotation: 0.3
    brightness: 0.2
    contrast: 0.2

# 模型配置
model:
  # 多模态融合参数
  fusion:
    fusion_type: "mmif_cddfuse"            # 融合方式: early/middle/cross_attention/mmif_cddfuse
    s2_channels: 4                          # Sentinel-2 通道数
    s1_channels: 2                          # Sentinel-1 通道数
    hidden_dim: 64                          # 隐藏层维度
    # MMIF-CDDFuse 特有参数
    num_blocks: [4, 4]                     # [编码器块数, 解码器块数]
    heads: [8, 8, 8]                       # [level1头数, level2头数, base特征头数]
    ffn_expansion_factor: 2                # FFN扩展因子
    bias: false                            # 是否使用偏置
    LayerNorm_type: "WithBias"             # LayerNorm类型
    # 兼容参数
    num_heads: 8                           # 注意力头数（向后兼容）
    
  # 弱监督分割参数
  weakly_supervised:
    region_growing:
      max_iterations: 10                   # 区域增长最大迭代次数
      confidence_threshold: 0.95           # 置信度阈值
      neighbor_size: 8                     # 邻域大小
      morphology_kernel: 3                 # 形态学操作核大小
    
    dual_classifier: true                  # 是否使用双分类器结构

# 训练配置
training:
  # 基本参数
  batch_size: 16                           # 批大小
  num_epochs: 200                          # 训练轮数
  learning_rate: 0.001                     # 学习率
  weight_decay: 0.0005                     # 权重衰减
  momentum: 0.9                            # 动量
  
  # 学习率调度
  scheduler:
    type: "cosine"                         # 调度器类型
    warmup_epochs: 10                      # 预热轮数
    min_lr: 0.00001                        # 最小学习率
  
  # 损失函数权重
  loss_weights:
    segmentation: 1.0                      # 分割损失权重
    expansion: 1.0                         # 扩展损失权重
    consistency: 1.0                       # 一致性损失权重
    
  # 评估设置
  eval_frequency: 10                       # 评估频率（轮数）
  save_frequency: 20                       # 保存频率（轮数）
  early_stopping_patience: 30              # 早停耐心值

# 测试配置
testing:
  batch_size: 1                            # 测试批大小
  overlap: 40                              # 滑动窗口重叠
  output_format: "geotiff"                 # 输出格式: geotiff/png
  
# 可视化配置
visualization:
  save_attention_maps: true                # 保存注意力图
  save_prediction_maps: true               # 保存预测图
  save_confidence_maps: true               # 保存置信度图

# 路径配置
paths:
  checkpoint_dir: "checkpoints/"           # 模型保存目录
  log_dir: "logs/"                         # 日志目录
  result_dir: "results/"                   # 结果保存目录
  
# 硬件配置
hardware:
  use_gpu: true                            # 是否使用GPU
  gpu_ids: [0]                             # GPU ID列表
  num_workers: 4                           # 数据加载进程数 