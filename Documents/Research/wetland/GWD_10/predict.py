import os
import argparse
import yaml
import torch
import torch.nn.functional as F
import numpy as np
import rasterio
from rasterio.windows import Window
from tqdm import tqdm
import json
from datetime import datetime

# 导入自定义模块
from models.weakly_net import create_weakly_segmentation_net
from utils.visualization import save_prediction_as_geotiff, WetlandVisualizer


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='湿地遥感影像分割预测')
    parser.add_argument('--config', type=str, default='config/config.yaml',
                        help='配置文件路径')
    parser.add_argument('--model_path', type=str, required=True,
                        help='训练好的模型路径')
    parser.add_argument('--s2_path', type=str, required=True,
                        help='Sentinel-2数据路径')
    parser.add_argument('--s1_path', type=str, required=True,
                        help='Sentinel-1数据路径')
    parser.add_argument('--output_dir', type=str, default='predictions/',
                        help='预测结果输出目录')
    parser.add_argument('--patch_size', type=int, default=128,
                        help='预测时的patch大小')
    parser.add_argument('--overlap', type=int, default=32,
                        help='patch重叠大小')
    parser.add_argument('--batch_size', type=int, default=16,
                        help='批处理大小')
    parser.add_argument('--save_confidence', action='store_true',
                        help='是否保存置信度图')
    parser.add_argument('--save_visualization', action='store_true',
                        help='是否保存可视化结果')
    parser.add_argument('--gpu_id', type=int, default=0,
                        help='使用的GPU ID')
    return parser.parse_args()


def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def load_model(model_path, config, device):
    """加载训练好的模型"""
    model = create_weakly_segmentation_net(config)
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location='cpu')
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    print(f"模型已加载: {model_path}")
    return model


def read_raster_data(file_path, bands=None, normalize=True):
    """
    读取栅格数据
    Args:
        file_path: 文件路径
        bands: 要读取的波段列表，None表示读取所有波段
        normalize: 是否归一化
    Returns:
        data: 栅格数据 [C, H, W]
        profile: 栅格信息
    """
    with rasterio.open(file_path) as src:
        profile = src.profile
        
        if bands is None:
            data = src.read()
        else:
            # 波段索引从1开始
            band_indices = [b + 1 for b in bands]
            data = src.read(band_indices)
        
        # 转换为float32
        data = data.astype(np.float32)
        
        # 归一化
        if normalize:
            if 'S2' in file_path or 'sentinel2' in file_path.lower():
                # Sentinel-2 归一化 (0-10000 -> 0-1)
                data = np.clip(data / 10000.0, 0, 1)
            elif 'S1' in file_path or 'sentinel1' in file_path.lower():
                # Sentinel-1 dB转线性并归一化
                data = np.power(10, data / 10.0)
                data = np.clip(data, 0, 1)
        
        return data, profile


def sliding_window_predict(model, s2_data, s1_data, patch_size, overlap, batch_size, device):
    """
    滑动窗口预测
    Args:
        model: 预测模型
        s2_data: Sentinel-2数据 [C, H, W]
        s1_data: Sentinel-1数据 [C, H, W]
        patch_size: patch大小
        overlap: 重叠大小
        batch_size: 批大小
        device: 设备
    Returns:
        prediction: 预测结果 [H, W]
        confidence: 置信度 [H, W]
    """
    _, height, width = s2_data.shape
    
    # 计算滑动窗口参数
    stride = patch_size - overlap
    
    # 计算需要的patch数量
    n_patches_h = (height - overlap) // stride + (1 if (height - overlap) % stride != 0 else 0)
    n_patches_w = (width - overlap) // stride + (1 if (width - overlap) % stride != 0 else 0)
    
    # 初始化结果
    prediction_sum = np.zeros((height, width), dtype=np.float32)
    confidence_sum = np.zeros((height, width), dtype=np.float32)
    count_map = np.zeros((height, width), dtype=np.float32)
    
    print(f"图像尺寸: {height}x{width}")
    print(f"需要预测 {n_patches_h}x{n_patches_w} = {n_patches_h * n_patches_w} 个patches")
    
    # 准备batch数据
    patches_s2 = []
    patches_s1 = []
    patch_coords = []
    
    model.eval()
    with torch.no_grad():
        for i in range(n_patches_h):
            for j in range(n_patches_w):
                # 计算patch坐标
                start_h = i * stride
                start_w = j * stride
                end_h = min(start_h + patch_size, height)
                end_w = min(start_w + patch_size, width)
                
                # 确保patch大小一致
                actual_h = end_h - start_h
                actual_w = end_w - start_w
                
                # 提取patch
                s2_patch = s2_data[:, start_h:end_h, start_w:end_w]
                s1_patch = s1_data[:, start_h:end_h, start_w:end_w]
                
                # 如果patch尺寸不够，进行padding
                if actual_h < patch_size or actual_w < patch_size:
                    pad_h = patch_size - actual_h
                    pad_w = patch_size - actual_w
                    
                    s2_patch = np.pad(s2_patch, ((0, 0), (0, pad_h), (0, pad_w)), mode='reflect')
                    s1_patch = np.pad(s1_patch, ((0, 0), (0, pad_h), (0, pad_w)), mode='reflect')
                
                patches_s2.append(s2_patch)
                patches_s1.append(s1_patch)
                patch_coords.append((start_h, start_w, end_h, end_w, actual_h, actual_w))
                
                # 当batch满了或者是最后一个patch时，进行预测
                if len(patches_s2) == batch_size or (i == n_patches_h - 1 and j == n_patches_w - 1):
                    # 转换为tensor
                    batch_s2 = torch.stack([torch.from_numpy(p) for p in patches_s2]).to(device)
                    batch_s1 = torch.stack([torch.from_numpy(p) for p in patches_s1]).to(device)
                    
                    # 预测
                    results = model(batch_s2, batch_s1, training=False)
                    
                    # 获取预测结果
                    if 'expanded_pred' in results:
                        base_prob = F.softmax(results['base_pred'], dim=1)
                        expanded_prob = F.softmax(results['expanded_pred'], dim=1)
                        final_prob = (base_prob + expanded_prob) / 2.0
                    else:
                        final_prob = F.softmax(results['base_pred'], dim=1)
                    
                    confidence_batch, predictions_batch = torch.max(final_prob, dim=1)
                    
                    # 转换为numpy
                    predictions_np = predictions_batch.cpu().numpy()
                    confidence_np = confidence_batch.cpu().numpy()
                    
                    # 将结果放回原图
                    for idx, (pred, conf, coords) in enumerate(zip(predictions_np, confidence_np, patch_coords)):
                        start_h, start_w, end_h, end_w, actual_h, actual_w = coords
                        
                        # 提取有效区域
                        pred_patch = pred[:actual_h, :actual_w]
                        conf_patch = conf[:actual_h, :actual_w]
                        
                        # 累加到结果中
                        prediction_sum[start_h:end_h, start_w:end_w] += pred_patch * conf_patch
                        confidence_sum[start_h:end_h, start_w:end_w] += conf_patch
                        count_map[start_h:end_h, start_w:end_w] += 1
                    
                    # 清空batch
                    patches_s2 = []
                    patches_s1 = []
                    patch_coords = []
    
    # 计算最终结果
    # 避免除零
    count_map = np.maximum(count_map, 1e-8)
    
    # 加权平均
    final_prediction = prediction_sum / confidence_sum
    final_prediction = np.where(confidence_sum > 0, final_prediction, 0)
    final_prediction = np.round(final_prediction).astype(np.uint8)
    
    # 平均置信度
    final_confidence = confidence_sum / count_map
    
    return final_prediction, final_confidence


def create_prediction_report(prediction, confidence, s2_path, s1_path, model_path, output_dir):
    """创建预测报告"""
    # 统计信息
    unique, counts = np.unique(prediction, return_counts=True)
    class_stats = dict(zip(unique.tolist(), counts.tolist()))
    
    total_pixels = prediction.size
    wetland_pixels = class_stats.get(1, 0)
    wetland_percentage = (wetland_pixels / total_pixels) * 100
    
    avg_confidence = np.mean(confidence)
    std_confidence = np.std(confidence)
    
    report = {
        'prediction_timestamp': datetime.now().isoformat(),
        'input_files': {
            'sentinel2': os.path.basename(s2_path),
            'sentinel1': os.path.basename(s1_path)
        },
        'model_file': os.path.basename(model_path),
        'image_info': {
            'height': prediction.shape[0],
            'width': prediction.shape[1],
            'total_pixels': total_pixels
        },
        'prediction_statistics': {
            'class_distribution': class_stats,
            'wetland_percentage': wetland_percentage,
            'non_wetland_percentage': 100 - wetland_percentage
        },
        'confidence_statistics': {
            'mean_confidence': float(avg_confidence),
            'std_confidence': float(std_confidence),
            'min_confidence': float(np.min(confidence)),
            'max_confidence': float(np.max(confidence))
        }
    }
    
    # 保存报告
    report_path = os.path.join(output_dir, 'prediction_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # Markdown报告
    md_report_path = os.path.join(output_dir, 'prediction_report.md')
    with open(md_report_path, 'w', encoding='utf-8') as f:
        f.write("# 湿地分割预测报告\n\n")
        f.write(f"**预测时间**: {report['prediction_timestamp']}\n\n")
        
        f.write("## 输入数据\n\n")
        f.write(f"- **Sentinel-2**: {report['input_files']['sentinel2']}\n")
        f.write(f"- **Sentinel-1**: {report['input_files']['sentinel1']}\n")
        f.write(f"- **模型**: {report['model_file']}\n\n")
        
        f.write("## 图像信息\n\n")
        f.write(f"- **尺寸**: {report['image_info']['height']} × {report['image_info']['width']}\n")
        f.write(f"- **总像素数**: {report['image_info']['total_pixels']:,}\n\n")
        
        f.write("## 预测统计\n\n")
        f.write(f"- **湿地覆盖率**: {wetland_percentage:.2f}%\n")
        f.write(f"- **非湿地覆盖率**: {100 - wetland_percentage:.2f}%\n")
        f.write(f"- **湿地像素数**: {wetland_pixels:,}\n")
        f.write(f"- **非湿地像素数**: {class_stats.get(0, 0):,}\n\n")
        
        f.write("## 置信度统计\n\n")
        f.write(f"- **平均置信度**: {avg_confidence:.4f}\n")
        f.write(f"- **置信度标准差**: {std_confidence:.4f}\n")
        f.write(f"- **最小置信度**: {np.min(confidence):.4f}\n")
        f.write(f"- **最大置信度**: {np.max(confidence):.4f}\n")
    
    print(f"预测报告已保存到: {report_path}")
    print(f"Markdown报告已保存到: {md_report_path}")


def main():
    # 解析参数
    args = parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置设备
    device = torch.device(f'cuda:{args.gpu_id}' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(args.output_dir, f"prediction_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    print("=" * 60)
    print("湿地遥感影像分割预测")
    print("=" * 60)
    print(f"Sentinel-2: {args.s2_path}")
    print(f"Sentinel-1: {args.s1_path}")
    print(f"模型: {args.model_path}")
    print(f"输出目录: {output_dir}")
    
    # 加载模型
    print("\n加载模型...")
    model = load_model(args.model_path, config, device)
    
    # 读取遥感数据
    print("\n读取遥感数据...")
    print("读取Sentinel-2数据...")
    s2_data, s2_profile = read_raster_data(
        args.s2_path, 
        bands=config['data']['s2_bands']
    )
    
    print("读取Sentinel-1数据...")
    s1_data, s1_profile = read_raster_data(
        args.s1_path, 
        bands=config['data']['s1_bands']
    )
    
    print(f"Sentinel-2形状: {s2_data.shape}")
    print(f"Sentinel-1形状: {s1_data.shape}")
    
    # 检查数据尺寸是否匹配
    if s2_data.shape[1:] != s1_data.shape[1:]:
        print("警告：Sentinel-2和Sentinel-1数据尺寸不匹配")
        # 可以在这里添加重采样代码
    
    # 进行预测
    print("\n开始预测...")
    prediction, confidence = sliding_window_predict(
        model, s2_data, s1_data, 
        args.patch_size, args.overlap, args.batch_size, device
    )
    
    print("预测完成！")
    
    # 保存预测结果
    print("\n保存预测结果...")
    
    # 保存分割结果
    pred_path = os.path.join(output_dir, 'wetland_prediction.tif')
    save_prediction_as_geotiff(prediction, args.s2_path, pred_path)
    print(f"分割结果已保存到: {pred_path}")
    
    # 保存置信度图
    if args.save_confidence:
        conf_path = os.path.join(output_dir, 'confidence_map.tif')
        
        # 更新profile用于保存置信度
        conf_profile = s2_profile.copy()
        conf_profile.update(dtype=rasterio.float32, count=1)
        
        with rasterio.open(conf_path, 'w', **conf_profile) as dst:
            dst.write(confidence, 1)
        
        print(f"置信度图已保存到: {conf_path}")
    
    # 生成可视化
    if args.save_visualization:
        print("\n生成可视化...")
        visualizer = WetlandVisualizer(['非湿地', '湿地'])
        
        # 创建RGB图像用于显示
        s2_rgb = s2_data[:3] if s2_data.shape[0] >= 3 else np.stack([s2_data[0]] * 3)
        s2_rgb = np.transpose(s2_rgb, (1, 2, 0))
        
        images = {
            'sentinel2': s2_rgb,
            'sentinel1': s1_data
        }
        
        # 可视化预测结果
        fig = visualizer.visualize_predictions(
            images=images,
            predictions=prediction,
            confidence=confidence,
            title="湿地分割预测结果"
        )
        
        vis_path = os.path.join(output_dir, 'prediction_visualization.png')
        fig.savefig(vis_path, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存到: {vis_path}")
    
    # 生成预测报告
    print("\n生成预测报告...")
    create_prediction_report(
        prediction, confidence, 
        args.s2_path, args.s1_path, args.model_path,
        output_dir
    )
    
    # 打印统计信息
    unique, counts = np.unique(prediction, return_counts=True)
    total_pixels = prediction.size
    wetland_pixels = counts[1] if len(counts) > 1 else 0
    wetland_percentage = (wetland_pixels / total_pixels) * 100
    
    print(f"\n预测统计:")
    print(f"- 图像尺寸: {prediction.shape}")
    print(f"- 湿地覆盖率: {wetland_percentage:.2f}%")
    print(f"- 平均置信度: {np.mean(confidence):.4f}")
    print(f"\n所有结果已保存到: {output_dir}")


if __name__ == '__main__':
    main() 