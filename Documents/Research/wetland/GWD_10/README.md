# 湿地遥感影像分割系统

## 项目概述

本项目实现了一个基于多模态融合和弱监督学习的湿地遥感影像分割系统，能够联合利用Sentinel-2光学数据和Sentinel-1 SAR数据，在仅有点样本标注的情况下实现高精度湿地分割。

## 🎯 核心创新点

### 1. MMIF-CDDFuse + CRGNet集成架构
- **相关性驱动的双分支特征分解**: 将融合特征分解为基础特征(低频全局)和细节特征(高频局部)
- **CRGNet双分类器**: 基础分类器+扩展分类器，支持动态区域增长
- **4类湿地分割**: 水体/草本沼泽/木本沼泽/泥滩的精细化分类
- **多分类Lovász-Softmax损失**: 针对IoU指标优化的损失函数

### 2. 两阶段训练策略
- **阶段1-区域增长**: 基于置信度的动态标签扩展，自适应迭代次数
- **阶段2-自训练**: 伪标签生成与模型微调，提升泛化能力
- **轻量化设计**: CRGNet(18.9M参数)比WeaklyNet(42M参数)轻量化55%

### 3. MMIF-CDDFuse多模态融合创新
- **关联驱动的双分支特征分解**: 将多模态特征分离为基础特征（低频全局信息）和细节特征（高频局部信息）
- **相关性约束机制**: 通过CorrelationLoss强制基础特征相关、细节特征去相关
- **Transformer架构**: 基于Restormer的跨模态注意力机制

### 4. 高级区域增长算法
- **动态置信度调整**: 根据训练进程自适应调整区域增长阈值
- **形态学后处理**: 自动去除边界噪声，提升扩展标签质量
- **迭代优化机制**: 限制最大迭代次数，避免过拟合

## 主要特性

- 🌍 **多模态数据融合**：支持Sentinel-2和Sentinel-1数据的深度融合
- 🎯 **弱监督学习**：支持点样本训练，无需像素级标注
- 🔍 **高精度分割**：输出10m分辨率的湿地分割结果
- 📊 **双网络架构**：提供高精度和轻量化两种选择
- 🚀 **灵活训练模式**：支持端到端训练和两阶段训练
- ⚡ **高效推理**：支持滑动窗口批量预测

## 项目结构

```
湿地遥感影像分割系统/
├── models/                      # 网络模型定义
│   ├── __init__.py             # 模型包初始化
│   ├── fusion_net.py           # 基础多模态融合网络（10KB）
│   ├── mmif_fusion_net.py      # MMIF-CDDFuse融合网络（18KB）
│   ├── weakly_net.py           # 弱监督分割网络（14KB）
│   ├── crgnet.py              # CRGNet网络实现（17KB）
│   └── loss_functions.py       # 损失函数定义（17KB）
├── data/                       # 数据处理模块
│   ├── __init__.py            
│   └── dataset.py             # 数据集处理（16KB）
├── utils/                      # 工具函数
│   ├── __init__.py            
│   ├── metrics.py             # 评估指标（12KB）
│   └── visualization.py       # 可视化工具（19KB）
├── config/                     # 配置文件
│   └── config.yaml            # 主配置文件（3.9KB）
├── train.py                   # 主训练脚本（17KB）
├── train_crgnet.py            # CRGNet两阶段训练脚本（19KB）
├── test.py                    # 测试脚本（12KB）
├── test_crgnet.py             # CRGNet测试脚本（8.9KB）
├── predict.py                 # 预测脚本（16KB）
├── run_example.py             # 示例运行脚本（5.5KB）
├── test_mmif_simple.py        # MMIF简单测试（4.7KB）
├── test_mmif_fusion.py        # MMIF融合测试（8.5KB）
├── checkpoints/               # 模型检查点（训练后生成）
├── results/                   # 结果输出（运行后生成）
├── logs/                      # 训练日志（训练后生成）
├── requirements.txt           # 依赖包列表
├── CRGNet_Integration_Guide.md # CRGNet集成指南（9.3KB）
├── MMIF_CDDFuse_Integration.md # MMIF集成文档（7.4KB）
├── 需求.txt                   # 原始需求文档（3.0KB）
└── README.md                  # 项目说明
```

## 快速开始

### 1. 环境配置

```bash
# 克隆项目
git clone <repository_url>
cd wetland-segmentation

# 安装依赖
pip install -r requirements.txt
```

**主要依赖包：**
- torch>=1.8.0
- torchvision>=0.9.0
- numpy>=1.19.0
- scikit-image>=0.18.0
- rasterio>=1.2.0
- einops>=0.3.0
- timm>=0.4.0

### 2. 数据准备

#### 2.1 遥感数据结构
```
data/
├── sentinel2/              # Sentinel-2 数据目录
│   ├── image1_S2.tif      # 命名格式: {image_name}_S2.tif
│   ├── image2_S2.tif
│   └── ...
├── sentinel1/              # Sentinel-1 数据目录
│   ├── image1_S1.tif      # 命名格式: {image_name}_S1.tif
│   ├── image2_S1.tif
│   └── ...
├── point_labels.shp        # 点样本标注文件
├── train_list.txt         # 训练数据列表
└── test_list.txt          # 测试数据列表
```

#### 2.2 点样本标注格式
**Shapefile格式 (.shp)**
- 包含Point几何类型
- 属性表包含`label`字段：0=水体，1=草本沼泽，2=木本沼泽，3=泥滩

**CSV格式 (.csv)**
```csv
longitude,latitude,label
116.123,39.456,0
116.124,39.457,1
116.125,39.458,2
116.126,39.459,3
...
```

#### 2.3 数据要求
- **Sentinel-2**: 10m分辨率，使用B2,B3,B4,B8波段
- **Sentinel-1**: 10m分辨率，使用VV,VH极化
- **投影坐标系**: 两种数据需要相同的坐标系统
- **像素对齐**: 确保两种数据在相同地理位置上对齐

### 3. 配置文件设置

编辑 `config/config.yaml` 文件：

```yaml
  # 数据配置
  data:
    sentinel2_dir: "data/sentinel2/"
    sentinel1_dir: "data/sentinel1/"
    point_labels_path: "data/point_labels.shp"
    num_classes: 4
    s2_bands: [1, 2, 3, 7]              # B2,B3,B4,B8
    s1_bands: [0, 1]                     # VV,VH

# 模型配置
model:
  fusion:
    fusion_type: "mmif_cddfuse"        # 融合方式选择
    s2_channels: 4
    s1_channels: 2
    hidden_dim: 64
    # MMIF-CDDFuse 特有参数
    num_blocks: [4, 4]
    heads: [8, 8, 8]
    ffn_expansion_factor: 2
    
  weakly_supervised:
    dual_classifier: true              # 双分类器
    region_growing:
      max_iterations: 10
      confidence_threshold: 0.95
```

### 4. 快速运行示例

```bash
# 运行完整演示流程
python run_example.py --mode all

# 分步运行
python run_example.py --mode train    # 仅训练
python run_example.py --mode test     # 仅测试
python run_example.py --mode predict  # 仅预测
```

## 详细使用指南

### 网络架构选择

#### 方案1: WeaklySegmentationNet（高精度）

```bash
# 训练（支持MMIF-CDDFuse融合）
python train.py \
    --config config/config.yaml \
    --gpu_ids 0 \
    --seed 42
```

**特点：**
- 参数量：42,144,248
- MMIF-CDDFuse多模态融合
- 端到端训练
- 支持相关性损失

#### 方案2: CRGNet（轻量化）

```bash
# 阶段1：区域增长训练
python train_crgnet.py \
    --stage 1 \
    --config config/config.yaml \
    --gpu_ids 0

# 阶段2：自训练优化
python train_crgnet.py \
    --stage 2 \
    --stage1_model checkpoints/best_stage1_model.pth \
    --config config/config.yaml
```

**特点：**
- 参数量：18,642,596（轻量化56%）
- 双分类器架构
- 两阶段训练策略
- 高级区域增长算法

### 测试模型

#### 标准测试
```bash
python test.py \
    --config config/config.yaml \
    --model_path checkpoints/best_model.pth \
    --output_dir test_results/ \
    --save_predictions \
    --save_visualizations
```

#### CRGNet测试
```bash
python test_crgnet.py
```

### 预测新数据

```bash
python predict.py \
    --config config/config.yaml \
    --model_path checkpoints/best_model.pth \
    --s2_path data/new_sentinel2.tif \
    --s1_path data/new_sentinel1.tif \
    --output_dir predictions/ \
    --save_confidence \
    --save_visualization
```

## 配置参数详解

### 融合策略配置
```yaml
model:
  fusion:
    fusion_type: "mmif_cddfuse"      # 可选：early/middle/cross_attention/mmif_cddfuse
    # MMIF-CDDFuse特有参数
    num_blocks: [4, 4]               # [编码器块数, 解码器块数]
    heads: [8, 8, 8]                 # 多头注意力头数
    ffn_expansion_factor: 2          # FFN扩展因子
    bias: false                      # 是否使用偏置
```

### 弱监督设置
```yaml
model:
  weakly_supervised:
    dual_classifier: true            # 是否使用双分类器
    region_growing:
      confidence_threshold: 0.95     # 置信度阈值
      max_iterations: 10             # 最大迭代次数
      neighbor_size: 8               # 邻域大小
      morphology_kernel: 3           # 形态学核大小
```

### 训练参数
```yaml
training:
  batch_size: 16
  learning_rate: 0.001
  num_epochs: 200
  scheduler:
    type: "cosine"                   # cosine/step/plateau
  loss_weights:
    segmentation: 1.0                # 分割损失权重
    expansion: 1.0                   # 扩展损失权重
    consistency: 1.0                 # 一致性损失权重
```

## 评估指标

### 主要指标
- **mIoU**: 平均交并比，衡量分割准确度
- **mF1**: 平均F1分数，综合精确率和召回率
- **OA**: 总体精度，正确分类的像素比例
- **Kappa**: Kappa系数，考虑随机一致性的准确度

### 类别指标
- **IoU**: 每个类别的交并比
- **F1-score**: 每个类别的F1分数
- **Precision**: 每个类别的精确率
- **Recall**: 每个类别的召回率

## 测试验证结果

### 网络架构对比
| 模型 | 参数量 | 特点 | 适用场景 |
|------|--------|------|----------|
| WeaklySegmentationNet | 42,144,248 | 高精度MMIF融合 | 精度优先 |
| CRGNet | 18,642,596 | 轻量化双分类器 | 效率优先 |

### 实际测试数据
```
CRGNet基本功能测试: ✅
- 总参数量: 18,642,596
- 输出形状: 正确
- 4类分割: 水体/草本沼泽/木本沼泽/泥滩
- 损失计算: 多分类Lovász-Softmax损失
- 伪标签生成: 成功

MMIF-CDDFuse + CRGNet集成: ✅
- 双分支特征分解: 基础特征 + 细节特征
- 相关性约束: 正常工作
- 两阶段训练: 支持区域增长 + 自训练
```

## 可视化功能

### 训练监控
- TensorBoard实时监控：损失曲线、评估指标、学习率
- 训练进度可视化：`visualizations/training_progress.png`
- 指标变化图：mIoU、F1-score变化趋势

### 结果分析
- 预测结果对比图：原图、真实标签、预测结果
- 注意力热图：MMIF-CDDFuse注意力机制可视化
- 置信度分布图：预测置信度统计分析
- 混淆矩阵热图：详细分类错误分析

### 示例输出文件
```
test_results/
├── test_report.json           # 详细测试指标
├── test_report.md            # Markdown格式报告
├── predictions/              # GeoTIFF预测结果
├── visualizations/           # 可视化图像
│   ├── confusion_matrix.png  # 混淆矩阵
│   ├── overview.png          # 结果总览
│   └── sample_*.png          # 样本预测图
```

## 实验管理

### 自动化实验管理
- **时间戳命名**: 每次实验自动生成唯一标识
- **配置保存**: 实验配置自动备份到输出目录
- **检查点管理**: 支持最佳模型、最佳F1、定期检查点保存
- **日志记录**: TensorBoard和文本日志双重记录

### 实验输出结构
```
checkpoints/experiment_20240101_120000/
├── config.yaml               # 实验配置备份
├── best_model.pth           # 最佳mIoU模型
├── best_f1_model.pth        # 最佳F1模型
├── checkpoint_epoch_*.pth   # 定期检查点
├── training_history.json   # 训练历史数据
└── visualizations/          # 训练可视化
```

## 常见问题

### Q1: 如何选择网络架构？
A: 
- **追求最高精度**: 选择WeaklySegmentationNet（42M参数）
- **需要轻量化**: 选择CRGNet（18M参数，效率提升56%）
- **资源受限**: 推荐CRGNet，支持两阶段训练优化

### Q2: 如何处理不同分辨率的数据？
A: 系统会自动将数据重采样到10m分辨率。确保输入数据的地理坐标系统一致。

### Q3: 内存不足怎么办？
A: 可以通过以下方式优化：
- 减少`batch_size`（默认16）
- 使用CRGNet轻量化架构
- 减少`hidden_dim`（默认64）

### Q4: MMIF-CDDFuse相关性损失异常？
A: 检查配置文件中的融合类型设置：
```yaml
model:
  fusion:
    fusion_type: "mmif_cddfuse"    # 确保正确设置
```

### Q5: 两阶段训练如何衔接？
A: 
```bash
# 先完成阶段1
python train_crgnet.py --stage 1 --config config.yaml

# 使用阶段1模型进行阶段2
python train_crgnet.py --stage 2 --stage1_model checkpoints/best_stage1_model.pth
```

## 扩展功能

### 已实现功能
1. **多融合策略**: early/middle/cross_attention/mmif_cddfuse
2. **双网络架构**: WeaklyNet和CRGNet可选
3. **多种损失函数**: Focal、Dice、Lovász-Softmax、相关性损失
4. **完整评估体系**: 包含多种分割指标
5. **可视化分析**: 训练监控、结果分析、注意力可视化

### 支持的扩展方向
1. **多时相分析**: 支持时间序列湿地变化检测
2. **多类别分割**: 扩展到更多土地覆盖类型
3. **DEM数据融合**: 增加地形信息
4. **不确定性量化**: 提供预测不确定性估计

## 技术支持

### 系统要求
- **操作系统**: Linux/macOS/Windows
- **Python**: ≥3.7
- **GPU**: 推荐使用GPU加速（支持CPU运行）
- **内存**: 推荐≥16GB RAM

### 性能优化建议
1. **使用SSD**: 提升数据加载速度
2. **GPU加速**: 显著提升训练和推理速度（测试通过）
3. **多进程**: 设置适当的`num_workers`（默认4）
4. **架构选择**: 根据资源选择合适网络架构

## 项目文档

- **CRGNet集成指南**: `CRGNet_Integration_Guide.md`（370行详细文档）
- **MMIF集成文档**: `MMIF_CDDFuse_Integration.md`（254行技术文档）
- **原始需求**: `需求.txt`（原始项目需求）

## 更新日志

### v2.0.0 (当前版本)
- ✅ 新增CRGNet轻量化网络（18M参数）
- ✅ 集成MMIF-CDDFuse多模态融合
- ✅ 实现两阶段训练策略
- ✅ 添加相关性损失机制
- ✅ 支持双网络架构选择
- ✅ 完善可视化和评估体系

### v1.0.0 (基础版本)
- ✅ 实现基础弱监督分割
- ✅ 支持Sentinel-1/2多模态融合
- ✅ 提供完整的训练、测试、预测流程

## 引用

如果本项目对您的研究有帮助，请考虑引用：

```bibtex
@misc{wetland_segmentation_2024,
  title={Multi-modal Weakly Supervised Wetland Segmentation with MMIF-CDDFuse and CRGNet},
  author={Your Name},
  year={2024},
  howpublished={\url{https://github.com/yourname/wetland-segmentation}}
}
``` 