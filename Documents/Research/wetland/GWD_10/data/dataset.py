import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import rasterio
import geopandas as gpd
from shapely.geometry import Point
import pandas as pd
from sklearn.model_selection import train_test_split
import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2
from typing import Tuple, List, Dict, Optional
import pickle


class WetlandDataset(Dataset):
    """湿地遥感数据集"""
    
    def __init__(self, 
                 sentinel2_dir: str,
                 sentinel1_dir: str,
                 point_labels_path: str,
                 image_list: List[str],
                 patch_size: Tuple[int, int] = (128, 128),
                 s2_bands: List[int] = [1, 2, 3, 7],  # B2, B3, B4, B8
                 s1_bands: List[int] = [0, 1],  # VV, VH
                 transform=None,
                 cache_data: bool = False):
        """
        Args:
            sentinel2_dir: Sentinel-2数据目录
            sentinel1_dir: Sentinel-1数据目录
            point_labels_path: 点标签文件路径
            image_list: 图像文件名列表
            patch_size: 图像块大小
            s2_bands: 使用的Sentinel-2波段
            s1_bands: 使用的Sentinel-1波段
            transform: 数据增强变换
            cache_data: 是否缓存数据到内存
        """
        self.sentinel2_dir = sentinel2_dir
        self.sentinel1_dir = sentinel1_dir
        self.point_labels_path = point_labels_path
        self.image_list = image_list
        self.patch_size = patch_size
        self.s2_bands = s2_bands
        self.s1_bands = s1_bands
        self.transform = transform
        self.cache_data = cache_data
        
        # 加载点标签
        self.point_labels = self._load_point_labels()
        
        # 生成样本列表
        self.samples = self._generate_samples()
        
        # 数据缓存
        if cache_data:
            self.data_cache = {}
        
        print(f"数据集初始化完成，共有 {len(self.samples)} 个样本")

    def _load_point_labels(self) -> gpd.GeoDataFrame:
        """加载点标签数据"""
        if self.point_labels_path.endswith('.shp'):
            return gpd.read_file(self.point_labels_path)
        elif self.point_labels_path.endswith('.csv'):
            df = pd.read_csv(self.point_labels_path)
            geometry = [Point(xy) for xy in zip(df['longitude'], df['latitude'])]
            return gpd.GeoDataFrame(df, geometry=geometry)
        else:
            raise ValueError("不支持的点标签文件格式")

    def _generate_samples(self) -> List[Dict]:
        """生成训练样本列表"""
        samples = []
        
        for image_name in self.image_list:
            # 获取图像的地理范围
            s2_path = os.path.join(self.sentinel2_dir, f"{image_name}_S2.tif")
            if not os.path.exists(s2_path):
                continue
                
            # 读取图像元数据
            with rasterio.open(s2_path) as src:
                bounds = src.bounds
                transform = src.transform
                height, width = src.height, src.width
            
            # 计算可以生成的patch数量
            patch_h, patch_w = self.patch_size
            n_patches_h = height // patch_h
            n_patches_w = width // patch_w
            
            # 生成所有可能的patch
            for i in range(n_patches_h):
                for j in range(n_patches_w):
                    # 计算patch的像素坐标
                    start_row = i * patch_h
                    start_col = j * patch_w
                    end_row = start_row + patch_h
                    end_col = start_col + patch_w
                    
                    # 计算patch的地理坐标
                    min_x = transform * (start_col, start_row)
                    max_x = transform * (end_col, end_row)
                    
                    # 检查该patch是否包含点标签
                    patch_bounds = (min_x[0], max_x[1], max_x[0], min_x[1])  # (minx, miny, maxx, maxy)
                    
                    # 查询该区域内的点标签
                    patch_points = self.point_labels.cx[patch_bounds[0]:patch_bounds[2], 
                                                        patch_bounds[1]:patch_bounds[3]]
                    
                    if len(patch_points) > 0:  # 只使用包含标签点的patch
                        sample = {
                            'image_name': image_name,
                            'patch_coords': (start_row, start_col, end_row, end_col),
                            'geo_bounds': patch_bounds,
                            'point_labels': patch_points
                        }
                        samples.append(sample)
        
        return samples

    def _load_image_patch(self, image_name: str, patch_coords: Tuple[int, int, int, int]) -> Tuple[np.ndarray, np.ndarray]:
        """加载图像patch"""
        start_row, start_col, end_row, end_col = patch_coords
        
        # 加载Sentinel-2数据
        s2_path = os.path.join(self.sentinel2_dir, f"{image_name}_S2.tif")
        with rasterio.open(s2_path) as src:
            s2_data = src.read(window=rasterio.windows.Window(start_col, start_row, 
                                                            end_col - start_col, 
                                                            end_row - start_row))
            s2_data = s2_data[self.s2_bands, :, :]  # 选择指定波段
        
        # 加载Sentinel-1数据
        s1_path = os.path.join(self.sentinel1_dir, f"{image_name}_S1.tif")
        with rasterio.open(s1_path) as src:
            s1_data = src.read(window=rasterio.windows.Window(start_col, start_row,
                                                            end_col - start_col,
                                                            end_row - start_row))
            s1_data = s1_data[self.s1_bands, :, :]  # 选择指定波段
        
        return s2_data.astype(np.float32), s1_data.astype(np.float32)

    def _create_point_label_mask(self, point_labels: gpd.GeoDataFrame, 
                                patch_coords: Tuple[int, int, int, int],
                                image_name: str) -> np.ndarray:
        """创建点标签掩码"""
        start_row, start_col, end_row, end_col = patch_coords
        height, width = end_row - start_row, end_col - start_col
        
        # 初始化掩码（255表示未标记）
        mask = np.full((height, width), 255, dtype=np.uint8)
        
        # 获取图像的地理变换信息
        s2_path = os.path.join(self.sentinel2_dir, f"{image_name}_S2.tif")
        with rasterio.open(s2_path) as src:
            transform = src.transform
        
        # 将点标签转换为像素坐标
        for idx, point in point_labels.iterrows():
            # 地理坐标转像素坐标
            col, row = rasterio.transform.rowcol(transform, point.geometry.x, point.geometry.y)
            
            # 转换为patch内的相对坐标
            rel_row = row - start_row
            rel_col = col - start_col
            
            # 检查是否在patch范围内
            if 0 <= rel_row < height and 0 <= rel_col < width:
                # 假设point_labels中有'label'列，0表示非湿地，1表示湿地
                label = int(point.get('label', 0))
                mask[rel_row, rel_col] = label
        
        return mask

    def _normalize_data(self, s2_data: np.ndarray, s1_data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """数据归一化"""
        # Sentinel-2归一化（假设反射率值在0-10000范围）
        s2_data = np.clip(s2_data / 10000.0, 0, 1)
        
        # Sentinel-1归一化（dB值转线性，然后归一化）
        s1_data = np.power(10, s1_data / 10.0)  # dB转线性
        s1_data = np.clip(s1_data, 0, 1)
        
        return s2_data, s1_data

    def __len__(self) -> int:
        return len(self.samples)

    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """获取单个样本"""
        sample = self.samples[idx]
        
        # 检查缓存
        if self.cache_data and idx in self.data_cache:
            return self.data_cache[idx]
        
        # 加载数据
        s2_data, s1_data = self._load_image_patch(sample['image_name'], sample['patch_coords'])
        
        # 创建点标签掩码
        point_mask = self._create_point_label_mask(sample['point_labels'], 
                                                  sample['patch_coords'],
                                                  sample['image_name'])
        
        # 数据归一化
        s2_data, s1_data = self._normalize_data(s2_data, s1_data)
        
        # 转换维度顺序：(C, H, W) -> (H, W, C)
        s2_data = np.transpose(s2_data, (1, 2, 0))
        s1_data = np.transpose(s1_data, (1, 2, 0))
        
        # 数据增强
        if self.transform:
            # 合并数据用于统一增强
            combined_image = np.concatenate([s2_data, s1_data], axis=2)
            
            augmented = self.transform(image=combined_image, mask=point_mask)
            
            combined_image = augmented['image']
            point_mask = augmented['mask']
            
            # 分离数据
            s2_data = combined_image[:len(self.s2_bands), :, :]
            s1_data = combined_image[len(self.s2_bands):, :, :]
        else:
            # 转换为tensor
            s2_data = torch.from_numpy(s2_data).permute(2, 0, 1)
            s1_data = torch.from_numpy(s1_data).permute(2, 0, 1)
            point_mask = torch.from_numpy(point_mask).long()
        
        result = {
            'sentinel2': s2_data,
            'sentinel1': s1_data,
            'point_labels': point_mask,
            'image_name': sample['image_name'],
            'patch_coords': sample['patch_coords']
        }
        
        # 缓存数据
        if self.cache_data:
            self.data_cache[idx] = result
        
        return result


def get_transforms(config: Dict, is_training: bool = True) -> A.Compose:
    """获取数据增强变换"""
    if is_training:
        aug_config = config['data']['augmentation']
        transform = A.Compose([
            A.HorizontalFlip(p=aug_config.get('horizontal_flip', 0.5)),
            A.VerticalFlip(p=aug_config.get('vertical_flip', 0.5)),
            A.RandomRotate90(p=aug_config.get('rotation', 0.3)),
            A.RandomBrightnessContrast(
                brightness_limit=aug_config.get('brightness', 0.2),
                contrast_limit=aug_config.get('contrast', 0.2),
                p=0.5
            ),
            A.OneOf([
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.5),
                A.GaussianBlur(blur_limit=3, p=0.5),
            ], p=0.3),
            ToTensorV2()
        ])
    else:
        transform = A.Compose([
            ToTensorV2()
        ])
    
    return transform


def create_data_loaders(config: Dict) -> Tuple[DataLoader, DataLoader]:
    """创建训练和测试数据加载器"""
    data_config = config['data']
    training_config = config['training']
    
    # 获取图像列表
    train_images = []
    test_images = []
    
    if os.path.exists(data_config['train_list']):
        with open(data_config['train_list'], 'r') as f:
            train_images = [line.strip() for line in f.readlines()]
    
    if os.path.exists(data_config['test_list']):
        with open(data_config['test_list'], 'r') as f:
            test_images = [line.strip() for line in f.readlines()]
    
    # 如果没有预定义的划分，自动划分
    if not train_images and not test_images:
        # 扫描目录获取所有图像
        all_images = []
        s2_files = os.listdir(data_config['sentinel2_dir'])
        for f in s2_files:
            if f.endswith('_S2.tif'):
                image_name = f.replace('_S2.tif', '')
                s1_file = f"{image_name}_S1.tif"
                if os.path.exists(os.path.join(data_config['sentinel1_dir'], s1_file)):
                    all_images.append(image_name)
        
        # 按8:2划分训练和测试集
        train_images, test_images = train_test_split(all_images, test_size=0.2, random_state=42)
    
    # 创建数据增强
    train_transform = get_transforms(config, is_training=True)
    test_transform = get_transforms(config, is_training=False)
    
    # 创建数据集
    train_dataset = WetlandDataset(
        sentinel2_dir=data_config['sentinel2_dir'],
        sentinel1_dir=data_config['sentinel1_dir'],
        point_labels_path=data_config['point_labels_path'],
        image_list=train_images,
        patch_size=tuple(data_config['input_size']),
        s2_bands=data_config['s2_bands'],
        s1_bands=data_config['s1_bands'],
        transform=train_transform,
        cache_data=False
    )
    
    test_dataset = WetlandDataset(
        sentinel2_dir=data_config['sentinel2_dir'],
        sentinel1_dir=data_config['sentinel1_dir'],
        point_labels_path=data_config['point_labels_path'],
        image_list=test_images,
        patch_size=tuple(data_config['input_size']),
        s2_bands=data_config['s2_bands'],
        s1_bands=data_config['s1_bands'],
        transform=test_transform,
        cache_data=False
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=training_config['batch_size'],
        shuffle=True,
        num_workers=config['hardware']['num_workers'],
        pin_memory=True,
        drop_last=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['testing']['batch_size'],
        shuffle=False,
        num_workers=config['hardware']['num_workers'],
        pin_memory=True
    )
    
    return train_loader, test_loader


def save_data_split(train_images: List[str], test_images: List[str], save_dir: str):
    """保存数据划分"""
    os.makedirs(save_dir, exist_ok=True)
    
    with open(os.path.join(save_dir, 'train_list.txt'), 'w') as f:
        for img in train_images:
            f.write(f"{img}\n")
    
    with open(os.path.join(save_dir, 'test_list.txt'), 'w') as f:
        for img in test_images:
            f.write(f"{img}\n")


if __name__ == "__main__":
    # 测试数据集
    config = {
        'data': {
            'sentinel2_dir': 'data/sentinel2/',
            'sentinel1_dir': 'data/sentinel1/',
            'point_labels_path': 'data/point_labels.shp',
            'train_list': 'data/train_list.txt',
            'test_list': 'data/test_list.txt',
            'input_size': [128, 128],
            'num_classes': 2,
            's2_bands': [1, 2, 3, 7],
            's1_bands': [0, 1],
            'augmentation': {
                'horizontal_flip': 0.5,
                'vertical_flip': 0.5,
                'rotation': 0.3,
                'brightness': 0.2,
                'contrast': 0.2
            }
        },
        'training': {
            'batch_size': 16
        },
        'testing': {
            'batch_size': 1
        },
        'hardware': {
            'num_workers': 4
        }
    }
    
    try:
        train_loader, test_loader = create_data_loaders(config)
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"测试集大小: {len(test_loader.dataset)}")
        
        # 测试加载一个batch
        batch = next(iter(train_loader))
        print(f"Sentinel-2形状: {batch['sentinel2'].shape}")
        print(f"Sentinel-1形状: {batch['sentinel1'].shape}")
        print(f"点标签形状: {batch['point_labels'].shape}")
        
    except Exception as e:
        print(f"数据加载测试失败: {e}")
        print("请确保数据目录和文件存在") 