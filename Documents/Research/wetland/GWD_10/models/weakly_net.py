import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import skimage.morphology as morph
from typing import Tuple, List
from .fusion_net import MultiModalFusionNet
from .mmif_fusion_net import MMIFFusionNet


class DoubleConv(nn.Module):
    """双卷积模块"""
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """下采样模块"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """上采样模块"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()

        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        # 调整尺寸匹配
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)


class BaseClassifier(nn.Module):
    """基础分类器"""
    def __init__(self, in_channels, num_classes, bilinear=True):
        super().__init__()
        self.n_channels = in_channels
        self.n_classes = num_classes
        self.bilinear = bilinear

        self.inc = DoubleConv(in_channels, 64)
        self.down1 = Down(64, 128)
        self.down2 = Down(128, 256)
        self.down3 = Down(256, 512)
        factor = 2 if bilinear else 1
        self.down4 = Down(512, 1024 // factor)
        self.up1 = Up(1024, 512 // factor, bilinear)
        self.up2 = Up(512, 256 // factor, bilinear)
        self.up3 = Up(256, 128 // factor, bilinear)
        self.up4 = Up(128, 64, bilinear)
        self.outc = nn.Conv2d(64, num_classes, kernel_size=1)

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        logits = self.outc(x)
        return logits


class ExpandedClassifier(nn.Module):
    """扩展分类器"""
    def __init__(self, in_channels, num_classes, bilinear=True):
        super().__init__()
        self.n_channels = in_channels
        self.n_classes = num_classes
        self.bilinear = bilinear

        # 更深的网络结构
        self.inc = DoubleConv(in_channels, 64)
        self.down1 = Down(64, 128)
        self.down2 = Down(128, 256)
        self.down3 = Down(256, 512)
        self.down4 = Down(512, 512)
        factor = 2 if bilinear else 1
        self.down5 = Down(512, 1024 // factor)
        
        self.up1 = Up(1024, 512 // factor, bilinear)     # 512 + 512 = 1024 -> 256
        self.up2 = Up(768, 256 // factor, bilinear)   # 256 + 512 = 768 -> 128  
        self.up3 = Up(384, 128 // factor, bilinear)   # 128 + 256 = 384 -> 64
        self.up4 = Up(192, 64 // factor, bilinear)    # 64 + 128 = 192 -> 32
        self.up5 = Up(96, 64, bilinear)               # 32 + 64 = 96 -> 64
        self.outc = nn.Conv2d(64, num_classes, kernel_size=1)

    def forward(self, x):
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        x6 = self.down5(x5)
        
        x = self.up1(x6, x5)
        x = self.up2(x, x4)
        x = self.up3(x, x3)
        x = self.up4(x, x2)
        x = self.up5(x, x1)
        logits = self.outc(x)
        return logits


class RegionGrowing:
    """区域增长算法"""
    def __init__(self, 
                 confidence_threshold=0.95, 
                 max_iterations=10, 
                 neighbor_size=8,
                 morphology_kernel=3):
        self.confidence_threshold = confidence_threshold
        self.max_iterations = max_iterations
        self.neighbor_size = neighbor_size
        self.morphology_kernel = morphology_kernel
        
        # 8连通邻域
        if neighbor_size == 8:
            self.delta_r = np.array([-1, -1, -1, 0, 0, 1, 1, 1])
            self.delta_c = np.array([-1, 0, 1, -1, 1, -1, 0, 1])
        else:  # 4连通邻域
            self.delta_r = np.array([-1, 0, 0, 1])
            self.delta_c = np.array([0, -1, 1, 0])
            
        # 形态学结构元素
        self.se = morph.square(self.morphology_kernel)

    def grow_regions(self, predictions, confidences, point_labels):
        """
        区域增长算法
        Args:
            predictions: 预测结果 [B, H, W]
            confidences: 置信度 [B, H, W]  
            point_labels: 点标签 [B, H, W]
        Returns:
            expanded_labels: 扩展后的标签 [B, H, W]
        """
        batch_size, height, width = predictions.shape
        expanded_labels = point_labels.copy()
        
        for b in range(batch_size):
            pred_b = predictions[b]
            conf_b = confidences[b]
            label_b = expanded_labels[b]
            
            # 区域增长迭代
            for iteration in range(self.max_iterations):
                updated = False
                
                # 找到标签边界像素
                label_mask = (label_b != 255).astype(np.uint8)
                erosion = morph.erosion(label_mask, self.se)
                boundary = label_mask - erosion
                
                boundary_coords = np.where(boundary > 0)
                
                for i in range(len(boundary_coords[0])):
                    r, c = boundary_coords[0][i], boundary_coords[1][i]
                    current_label = label_b[r, c]
                    
                    # 检查邻域
                    for j in range(len(self.delta_r)):
                        nr = r + self.delta_r[j]
                        nc = c + self.delta_c[j]
                        
                        # 边界检查
                        if 0 <= nr < height and 0 <= nc < width:
                            if label_b[nr, nc] == 255:  # 未标记像素
                                # 检查预测和置信度
                                pred_neighbor = pred_b[nr, nc]
                                conf_neighbor = conf_b[nr, nc]
                                
                                if (pred_neighbor == current_label and 
                                    conf_neighbor > self.confidence_threshold):
                                    label_b[nr, nc] = current_label
                                    updated = True
                
                if not updated:
                    break
            
            expanded_labels[b] = label_b
        
        return expanded_labels


class WeaklySegmentationNet(nn.Module):
    """弱监督分割网络"""
    def __init__(self, 
                 s2_channels=4, 
                 s1_channels=2, 
                 num_classes=2,
                 hidden_dim=64,
                 fusion_type='cross_attention',
                 use_dual_classifier=True,
                 region_growing_config=None,
                 fusion_config=None):
        super().__init__()
        
        self.num_classes = num_classes
        self.use_dual_classifier = use_dual_classifier
        self.fusion_type = fusion_type
        
        # 根据融合类型选择不同的融合网络
        if fusion_type == 'mmif_cddfuse':
            if fusion_config is None:
                fusion_config = {
                    'num_blocks': [4, 4],
                    'heads': [8, 8, 8],
                    'ffn_expansion_factor': 2,
                    'bias': False
                }
            self.fusion_net = MMIFFusionNet(
                s2_channels=s2_channels,
                s1_channels=s1_channels,
                hidden_dim=hidden_dim,
                **fusion_config
            )
        else:
            # 使用原来的多模态融合网络
            self.fusion_net = MultiModalFusionNet(
                s2_channels=s2_channels,
                s1_channels=s1_channels,
                hidden_dim=hidden_dim,
                fusion_type=fusion_type
            )
        
        # 基础分类器
        self.base_classifier = BaseClassifier(hidden_dim, num_classes)
        
        # 扩展分类器（用于弱监督学习）
        if use_dual_classifier:
            self.expanded_classifier = ExpandedClassifier(hidden_dim, num_classes)
        
        # 区域增长算法
        if region_growing_config is None:
            region_growing_config = {}
        self.region_growing = RegionGrowing(**region_growing_config)

    def forward(self, s2_data, s1_data, point_labels=None, training=True):
        """
        前向传播
        Args:
            s2_data: Sentinel-2 数据 [B, C_s2, H, W]
            s1_data: Sentinel-1 数据 [B, C_s1, H, W]
            point_labels: 点标签 [B, H, W] (训练时使用)
            training: 是否为训练模式
        Returns:
            base_pred: 基础分类器预测 [B, num_classes, H, W]
            expanded_pred: 扩展分类器预测 [B, num_classes, H, W] (如果使用双分类器)
            expanded_labels: 扩展标签 [B, H, W] (训练时返回)
        """
        # 多模态特征融合
        if self.fusion_type == 'mmif_cddfuse':
            fused_features, base_feature, detail_feature = self.fusion_net(s2_data, s1_data)
            # 保存额外的特征用于后续分析
            s2_feat, s1_feat = base_feature, detail_feature
        else:
            fused_features, s2_feat, s1_feat = self.fusion_net(s2_data, s1_data)
        
        # 基础分类器预测
        base_pred = self.base_classifier(fused_features)
        
        results = {'base_pred': base_pred}
        
        if self.use_dual_classifier:
            # 扩展分类器预测
            expanded_pred = self.expanded_classifier(fused_features)
            results['expanded_pred'] = expanded_pred
        
        # 训练时进行区域增长
        if training and point_labels is not None:
            # 获取预测类别和置信度
            base_prob = F.softmax(base_pred, dim=1)
            base_conf, base_class = torch.max(base_prob, dim=1)
            
            # 转换为numpy进行区域增长
            base_class_np = base_class.detach().cpu().numpy()
            base_conf_np = base_conf.detach().cpu().numpy()
            point_labels_np = point_labels.detach().cpu().numpy()
            
            # 区域增长
            expanded_labels_np = self.region_growing.grow_regions(
                base_class_np, base_conf_np, point_labels_np
            )
            
            # 转换回tensor
            expanded_labels = torch.from_numpy(expanded_labels_np).to(s2_data.device)
            results['expanded_labels'] = expanded_labels
        
        return results

    def get_attention_maps(self, s2_data, s1_data):
        """获取注意力图"""
        fused_features, s2_feat, s1_feat = self.fusion_net(s2_data, s1_data)
        
        # 这里可以从融合网络中提取注意力权重
        attention_maps = {}
        if hasattr(self.fusion_net.fusion, 'cross_attn_s2_s1'):
            # 如果使用跨注意力融合，可以提取注意力权重
            attention_maps['s2_to_s1'] = None  # 实际实现中需要保存注意力权重
            attention_maps['s1_to_s2'] = None
        
        return attention_maps


def create_weakly_segmentation_net(config):
    """根据配置创建弱监督分割网络"""
    model_config = config['model']
    fusion_config = model_config['fusion']
    weakly_config = model_config['weakly_supervised']
    
    # 为MMIF融合网络准备配置参数
    mmif_config = None
    if fusion_config['fusion_type'] == 'mmif_cddfuse':
        mmif_config = {
            'num_blocks': fusion_config.get('num_blocks', [4, 4]),
            'heads': fusion_config.get('heads', [8, 8, 8]),
            'ffn_expansion_factor': fusion_config.get('ffn_expansion_factor', 2),
            'bias': fusion_config.get('bias', False)
        }
    
    return WeaklySegmentationNet(
        s2_channels=fusion_config['s2_channels'],
        s1_channels=fusion_config['s1_channels'],
        num_classes=config['data']['num_classes'],
        hidden_dim=fusion_config['hidden_dim'],
        fusion_type=fusion_config['fusion_type'],
        use_dual_classifier=weakly_config['dual_classifier'],
        region_growing_config=weakly_config['region_growing'],
        fusion_config=mmif_config
    )


if __name__ == "__main__":
    # 测试网络
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model = WeaklySegmentationNet(
        s2_channels=4,
        s1_channels=2,
        num_classes=2,
        hidden_dim=64,
        fusion_type='cross_attention',
        use_dual_classifier=True
    ).to(device)
    
    # 模拟输入
    batch_size = 2
    s2_input = torch.randn(batch_size, 4, 128, 128).to(device)
    s1_input = torch.randn(batch_size, 2, 128, 128).to(device)
    point_labels = torch.randint(0, 2, (batch_size, 128, 128)).to(device)
    
    # 训练模式
    model.train()
    results = model(s2_input, s1_input, point_labels, training=True)
    
    print(f"基础预测形状: {results['base_pred'].shape}")
    if 'expanded_pred' in results:
        print(f"扩展预测形状: {results['expanded_pred'].shape}")
    if 'expanded_labels' in results:
        print(f"扩展标签形状: {results['expanded_labels'].shape}")
    print(f"总参数量: {sum(p.numel() for p in model.parameters())}") 