import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numbers
from einops import rearrange
try:
    from timm.layers import DropPath, to_2tuple, trunc_normal_
except ImportError:
    from timm.models.layers import DropPath, to_2tuple, trunc_normal_


def drop_path(x, drop_prob: float = 0., training: bool = False):
    """Drop paths (Stochastic Depth) per sample"""
    if drop_prob == 0. or not training:
        return x
    keep_prob = 1 - drop_prob
    shape = (x.shape[0],) + (1,) * (x.ndim - 1)
    random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
    random_tensor.floor_()
    output = x.div(keep_prob) * random_tensor
    return output


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample"""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        return drop_path(x, self.drop_prob, self.training)


# ========== Layer Norm Implementations ==========
def to_3d(x):
    return rearrange(x, 'b c h w -> b (h w) c')


def to_4d(x, h, w):
    return rearrange(x, 'b (h w) c -> b c h w', h=h, w=w)


class BiasFree_LayerNorm(nn.Module):
    def __init__(self, normalized_shape):
        super(BiasFree_LayerNorm, self).__init__()
        if isinstance(normalized_shape, numbers.Integral):
            normalized_shape = (normalized_shape,)
        normalized_shape = torch.Size(normalized_shape)
        assert len(normalized_shape) == 1
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.normalized_shape = normalized_shape

    def forward(self, x):
        sigma = x.var(-1, keepdim=True, unbiased=False)
        return x / torch.sqrt(sigma+1e-5) * self.weight


class WithBias_LayerNorm(nn.Module):
    def __init__(self, normalized_shape):
        super(WithBias_LayerNorm, self).__init__()
        if isinstance(normalized_shape, numbers.Integral):
            normalized_shape = (normalized_shape,)
        normalized_shape = torch.Size(normalized_shape)
        assert len(normalized_shape) == 1
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.normalized_shape = normalized_shape

    def forward(self, x):
        mu = x.mean(-1, keepdim=True)
        sigma = x.var(-1, keepdim=True, unbiased=False)
        return (x - mu) / torch.sqrt(sigma+1e-5) * self.weight + self.bias


class LayerNorm(nn.Module):
    def __init__(self, dim, LayerNorm_type='WithBias'):
        super(LayerNorm, self).__init__()
        if LayerNorm_type == 'BiasFree':
            self.body = BiasFree_LayerNorm(dim)
        else:
            self.body = WithBias_LayerNorm(dim)

    def forward(self, x):
        h, w = x.shape[-2:]
        return to_4d(self.body(to_3d(x)), h, w)


# ========== Attention Mechanisms ==========
class AttentionBase(nn.Module):
    """基础注意力模块，用于处理低频全局特征"""
    def __init__(self, dim, num_heads=8, qkv_bias=False):
        super(AttentionBase, self).__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = nn.Parameter(torch.ones(num_heads, 1, 1))
        self.qkv1 = nn.Conv2d(dim, dim*3, kernel_size=1, bias=qkv_bias)
        self.qkv2 = nn.Conv2d(dim*3, dim*3, kernel_size=3, padding=1, bias=qkv_bias)
        self.proj = nn.Conv2d(dim, dim, kernel_size=1, bias=qkv_bias)

    def forward(self, x):
        b, c, h, w = x.shape
        qkv = self.qkv2(self.qkv1(x))
        q, k, v = qkv.chunk(3, dim=1)
        
        q = rearrange(q, 'b (head c) h w -> b head c (h w)', head=self.num_heads)
        k = rearrange(k, 'b (head c) h w -> b head c (h w)', head=self.num_heads)
        v = rearrange(v, 'b (head c) h w -> b head c (h w)', head=self.num_heads)
        
        q = torch.nn.functional.normalize(q, dim=-1)
        k = torch.nn.functional.normalize(k, dim=-1)
        
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        
        out = (attn @ v)
        out = rearrange(out, 'b head c (h w) -> b (head c) h w', head=self.num_heads, h=h, w=w)
        out = self.proj(out)
        return out


class Attention(nn.Module):
    """Multi-DConv Head Transposed Self-Attention (MDTA)"""
    def __init__(self, dim, num_heads, bias):
        super(Attention, self).__init__()
        self.num_heads = num_heads
        self.temperature = nn.Parameter(torch.ones(num_heads, 1, 1))

        self.qkv = nn.Conv2d(dim, dim*3, kernel_size=1, bias=bias)
        self.qkv_dwconv = nn.Conv2d(dim*3, dim*3, kernel_size=3, stride=1, padding=1, groups=dim*3, bias=bias)
        self.project_out = nn.Conv2d(dim, dim, kernel_size=1, bias=bias)

    def forward(self, x):
        b, c, h, w = x.shape
        qkv = self.qkv_dwconv(self.qkv(x))
        q, k, v = qkv.chunk(3, dim=1)

        q = rearrange(q, 'b (head c) h w -> b head c (h w)', head=self.num_heads)
        k = rearrange(k, 'b (head c) h w -> b head c (h w)', head=self.num_heads)
        v = rearrange(v, 'b (head c) h w -> b head c (h w)', head=self.num_heads)

        q = torch.nn.functional.normalize(q, dim=-1)
        k = torch.nn.functional.normalize(k, dim=-1)

        attn = (q @ k.transpose(-2, -1)) * self.temperature
        attn = attn.softmax(dim=-1)

        out = (attn @ v)
        out = rearrange(out, 'b head c (h w) -> b (head c) h w', head=self.num_heads, h=h, w=w)
        out = self.project_out(out)
        return out


# ========== Feed Forward Networks ==========
class Mlp(nn.Module):
    """MLP as used in Vision Transformer"""
    def __init__(self, in_features, hidden_features=None, ffn_expansion_factor=2, bias=False):
        super().__init__()
        hidden_features = int(in_features * ffn_expansion_factor)

        self.project_in = nn.Conv2d(in_features, hidden_features*2, kernel_size=1, bias=bias)
        self.dwconv = nn.Conv2d(hidden_features*2, hidden_features*2, kernel_size=3,
                                stride=1, padding=1, groups=hidden_features, bias=bias)
        self.project_out = nn.Conv2d(hidden_features, in_features, kernel_size=1, bias=bias)

    def forward(self, x):
        x = self.project_in(x)
        x1, x2 = self.dwconv(x).chunk(2, dim=1)
        x = F.gelu(x1) * x2
        x = self.project_out(x)
        return x


class FeedForward(nn.Module):
    """Gated-Dconv Feed-Forward Network (GDFN)"""
    def __init__(self, dim, ffn_expansion_factor, bias):
        super(FeedForward, self).__init__()
        hidden_features = int(dim * ffn_expansion_factor)

        self.project_in = nn.Conv2d(dim, hidden_features*2, kernel_size=1, bias=bias)
        self.dwconv = nn.Conv2d(hidden_features*2, hidden_features*2, kernel_size=3,
                                stride=1, padding=1, groups=hidden_features*2, bias=bias)
        self.project_out = nn.Conv2d(hidden_features, dim, kernel_size=1, bias=bias)

    def forward(self, x):
        x = self.project_in(x)
        x1, x2 = self.dwconv(x).chunk(2, dim=1)
        x = F.gelu(x1) * x2
        x = self.project_out(x)
        return x


# ========== Feature Extraction Modules ==========
class BaseFeatureExtraction(nn.Module):
    """基础特征提取，用于提取低频全局特征"""
    def __init__(self, dim, num_heads, ffn_expansion_factor=1., qkv_bias=False):
        super(BaseFeatureExtraction, self).__init__()
        self.norm1 = LayerNorm(dim, 'WithBias')
        self.attn = AttentionBase(dim, num_heads=num_heads, qkv_bias=qkv_bias)
        self.norm2 = LayerNorm(dim, 'WithBias')
        self.mlp = Mlp(in_features=dim, ffn_expansion_factor=ffn_expansion_factor)

    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x


class InvertedResidualBlock(nn.Module):
    """可逆残差块，用于细节特征提取"""
    def __init__(self, inp, oup, expand_ratio):
        super(InvertedResidualBlock, self).__init__()
        hidden_dim = int(inp * expand_ratio)
        self.bottleneckBlock = nn.Sequential(
            # pw
            nn.Conv2d(inp, hidden_dim, 1, bias=False),
            nn.ReLU6(inplace=True),
            # dw
            nn.ReflectionPad2d(1),
            nn.Conv2d(hidden_dim, hidden_dim, 3, groups=hidden_dim, bias=False),
            nn.ReLU6(inplace=True),
            # pw-linear
            nn.Conv2d(hidden_dim, oup, 1, bias=False),
        )

    def forward(self, x):
        return self.bottleneckBlock(x)


class DetailNode(nn.Module):
    """细节节点，使用INN结构"""
    def __init__(self, channels=32):
        super(DetailNode, self).__init__()
        self.theta_phi = InvertedResidualBlock(inp=channels, oup=channels, expand_ratio=2)
        self.theta_rho = InvertedResidualBlock(inp=channels, oup=channels, expand_ratio=2)
        self.theta_eta = InvertedResidualBlock(inp=channels, oup=channels, expand_ratio=2)
        self.shffleconv = nn.Conv2d(channels*2, channels*2, kernel_size=1, stride=1, padding=0, bias=True)

    def separateFeature(self, x):
        z1, z2 = x[:, :x.shape[1]//2], x[:, x.shape[1]//2:x.shape[1]]
        return z1, z2

    def forward(self, z1, z2):
        z1, z2 = self.separateFeature(self.shffleconv(torch.cat((z1, z2), dim=1)))
        z2 = z2 + self.theta_phi(z1)
        z1 = z1 * torch.exp(self.theta_rho(z2)) + self.theta_eta(z2)
        return z1, z2


class DetailFeatureExtraction(nn.Module):
    """细节特征提取，用于提取高频局部特征"""
    def __init__(self, channels=64, num_layers=3):
        super(DetailFeatureExtraction, self).__init__()
        self.channels = channels
        INNmodules = [DetailNode(channels//2) for _ in range(num_layers)]
        self.net = nn.Sequential(*INNmodules)

    def forward(self, x):
        z1, z2 = x[:, :x.shape[1]//2], x[:, x.shape[1]//2:x.shape[1]]
        for layer in self.net:
            z1, z2 = layer(z1, z2)
        return torch.cat((z1, z2), dim=1)


# ========== Transformer Blocks ==========
class TransformerBlock(nn.Module):
    def __init__(self, dim, num_heads, ffn_expansion_factor, bias, LayerNorm_type):
        super(TransformerBlock, self).__init__()
        self.norm1 = LayerNorm(dim, LayerNorm_type)
        self.attn = Attention(dim, num_heads, bias)
        self.norm2 = LayerNorm(dim, LayerNorm_type)
        self.ffn = FeedForward(dim, ffn_expansion_factor, bias)

    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.ffn(self.norm2(x))
        return x


class OverlapPatchEmbed(nn.Module):
    """重叠patch嵌入"""
    def __init__(self, in_c=3, embed_dim=48, bias=False):
        super(OverlapPatchEmbed, self).__init__()
        self.proj = nn.Conv2d(in_c, embed_dim, kernel_size=3, stride=1, padding=1, bias=bias)

    def forward(self, x):
        x = self.proj(x)
        return x


# ========== Multi-Modal Fusion Encoder ==========
class MMIFEncoder(nn.Module):
    """多模态图像融合编码器，基于CDDFuse"""
    def __init__(self,
                 inp_channels=6,  # S2(4) + S1(2)
                 dim=64,
                 num_blocks=[4, 4],
                 heads=[8, 8, 8],
                 ffn_expansion_factor=2,
                 bias=False,
                 LayerNorm_type='WithBias'):
        super(MMIFEncoder, self).__init__()
        
        self.patch_embed = OverlapPatchEmbed(inp_channels, dim)
        
        # Transformer编码器层
        self.encoder_level1 = nn.Sequential(*[
            TransformerBlock(dim=dim, num_heads=heads[0], ffn_expansion_factor=ffn_expansion_factor,
                           bias=bias, LayerNorm_type=LayerNorm_type) 
            for i in range(num_blocks[0])
        ])
        
        # 基础特征提取（低频全局特征）
        self.baseFeature = BaseFeatureExtraction(dim=dim, num_heads=heads[2])
        
        # 细节特征提取（高频局部特征）
        self.detailFeature = DetailFeatureExtraction(channels=dim)

    def forward(self, inp_img):
        # Patch嵌入
        inp_enc_level1 = self.patch_embed(inp_img)
        
        # Transformer编码
        out_enc_level1 = self.encoder_level1(inp_enc_level1)
        
        # 特征分解
        base_feature = self.baseFeature(out_enc_level1)      # 低频全局特征
        detail_feature = self.detailFeature(out_enc_level1)  # 高频局部特征
        
        return base_feature, detail_feature, out_enc_level1


class MMIFDecoder(nn.Module):
    """多模态图像融合解码器"""
    def __init__(self,
                 out_channels=64,  # 输出特征维度
                 dim=64,
                 num_blocks=[4, 4],
                 heads=[8, 8, 8],
                 ffn_expansion_factor=2,
                 bias=False,
                 LayerNorm_type='WithBias'):
        super(MMIFDecoder, self).__init__()
        
        # 特征融合
        self.reduce_channel = nn.Conv2d(int(dim*2), int(dim), kernel_size=1, bias=bias)
        
        # Transformer解码器层
        self.encoder_level2 = nn.Sequential(*[
            TransformerBlock(dim=dim, num_heads=heads[1], ffn_expansion_factor=ffn_expansion_factor,
                           bias=bias, LayerNorm_type=LayerNorm_type) 
            for i in range(num_blocks[1])
        ])
        
        # 输出层
        self.output = nn.Sequential(
            nn.Conv2d(int(dim), int(dim)//2, kernel_size=3, stride=1, padding=1, bias=bias),
            nn.LeakyReLU(),
            nn.Conv2d(int(dim)//2, out_channels, kernel_size=3, stride=1, padding=1, bias=bias),
        )

    def forward(self, base_feature, detail_feature):
        # 特征融合
        out_enc_level0 = torch.cat((base_feature, detail_feature), dim=1)
        out_enc_level0 = self.reduce_channel(out_enc_level0)
        
        # Transformer解码
        out_enc_level1 = self.encoder_level2(out_enc_level0)
        
        # 输出融合特征
        fused_features = self.output(out_enc_level1)
        
        return fused_features, out_enc_level0


# ========== Complete Multi-Modal Fusion Network ==========
class MMIFFusionNet(nn.Module):
    """完整的多模态图像融合网络，基于CDDFuse"""
    def __init__(self,
                 s2_channels=4,
                 s1_channels=2,
                 hidden_dim=64,
                 num_blocks=[4, 4],
                 heads=[8, 8, 8],
                 ffn_expansion_factor=2,
                 bias=False):
        super(MMIFFusionNet, self).__init__()
        
        # 输入通道数
        inp_channels = s2_channels + s1_channels
        
        # 编码器
        self.encoder = MMIFEncoder(
            inp_channels=inp_channels,
            dim=hidden_dim,
            num_blocks=num_blocks,
            heads=heads,
            ffn_expansion_factor=ffn_expansion_factor,
            bias=bias
        )
        
        # 解码器
        self.decoder = MMIFDecoder(
            out_channels=hidden_dim,
            dim=hidden_dim,
            num_blocks=num_blocks,
            heads=heads,
            ffn_expansion_factor=ffn_expansion_factor,
            bias=bias
        )

    def forward(self, s2_data, s1_data):
        """
        前向传播
        Args:
            s2_data: Sentinel-2数据 [B, C_s2, H, W]
            s1_data: Sentinel-1数据 [B, C_s1, H, W]
        Returns:
            fused_features: 融合后的特征 [B, hidden_dim, H, W]
            base_feature: 基础特征 [B, hidden_dim, H, W]
            detail_feature: 细节特征 [B, hidden_dim, H, W]
        """
        # 拼接输入
        combined_input = torch.cat([s2_data, s1_data], dim=1)
        
        # 编码和特征分解
        base_feature, detail_feature, _ = self.encoder(combined_input)
        
        # 解码和特征融合
        fused_features, _ = self.decoder(base_feature, detail_feature)
        
        return fused_features, base_feature, detail_feature

    def get_correlation_loss(self, base_feature_s2, base_feature_s1, detail_feature_s2, detail_feature_s1):
        """
        计算相关性损失：使基础特征相关，细节特征不相关
        """
        # 基础特征相关性损失（应该相关）
        base_s2_flat = base_feature_s2.view(base_feature_s2.size(0), -1)
        base_s1_flat = base_feature_s1.view(base_feature_s1.size(0), -1)
        
        # 计算余弦相似度
        base_cosine_sim = F.cosine_similarity(base_s2_flat, base_s1_flat, dim=1)
        base_correlation_loss = 1 - base_cosine_sim.mean()  # 最大化相关性
        
        # 细节特征去相关性损失（应该不相关）
        detail_s2_flat = detail_feature_s2.view(detail_feature_s2.size(0), -1)
        detail_s1_flat = detail_feature_s1.view(detail_feature_s1.size(0), -1)
        
        # 计算余弦相似度
        detail_cosine_sim = F.cosine_similarity(detail_s2_flat, detail_s1_flat, dim=1)
        detail_decorrelation_loss = torch.abs(detail_cosine_sim).mean()  # 最小化相关性
        
        return base_correlation_loss + detail_decorrelation_loss


def create_mmif_fusion_net(config):
    """根据配置创建MMIF融合网络"""
    fusion_config = config['model']['fusion']
    
    return MMIFFusionNet(
        s2_channels=fusion_config['s2_channels'],
        s1_channels=fusion_config['s1_channels'],
        hidden_dim=fusion_config['hidden_dim'],
        num_blocks=fusion_config.get('num_blocks', [4, 4]),
        heads=fusion_config.get('heads', [8, 8, 8]),
        ffn_expansion_factor=fusion_config.get('ffn_expansion_factor', 2),
        bias=fusion_config.get('bias', False)
    )


if __name__ == "__main__":
    # 测试网络
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model = MMIFFusionNet(
        s2_channels=4,
        s1_channels=2,
        hidden_dim=64
    ).to(device)
    
    # 模拟输入
    s2_input = torch.randn(2, 4, 128, 128).to(device)
    s1_input = torch.randn(2, 2, 128, 128).to(device)
    
    fused, base_feat, detail_feat = model(s2_input, s1_input)
    
    print(f"S2输入形状: {s2_input.shape}")
    print(f"S1输入形状: {s1_input.shape}")
    print(f"融合特征形状: {fused.shape}")
    print(f"基础特征形状: {base_feat.shape}")
    print(f"细节特征形状: {detail_feat.shape}")
    print(f"参数量: {sum(p.numel() for p in model.parameters()):,}") 