import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numbers
from einops import rearrange
from timm.models.layers import DropPath, to_2tuple, trunc_normal_


def drop_path(x, drop_prob: float = 0., training: bool = False):
    """Drop paths (Stochastic Depth) per sample"""
    if drop_prob == 0. or not training:
        return x
    keep_prob = 1 - drop_prob
    shape = (x.shape[0],) + (1,) * (x.ndim - 1)
    random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
    random_tensor.floor_()
    output = x.div(keep_prob) * random_tensor
    return output


class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample"""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        return drop_path(x, self.drop_prob, self.training)


def to_3d(x):
    return rearrange(x, 'b c h w -> b (h w) c')


def to_4d(x, h, w):
    return rearrange(x, 'b (h w) c -> b c h w', h=h, w=w)


class LayerNorm(nn.Module):
    def __init__(self, dim, eps=1e-6):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(dim))
        self.bias = nn.Parameter(torch.zeros(dim))
        self.eps = eps

    def forward(self, x):
        h, w = x.shape[-2:]
        x = to_3d(x)
        mean = x.mean(-1, keepdim=True)
        var = x.var(-1, keepdim=True)
        x = (x - mean) / torch.sqrt(var + self.eps) * self.weight + self.bias
        return to_4d(x, h, w)


class CrossAttention(nn.Module):
    """跨模态注意力机制"""
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.q_conv = nn.Conv2d(dim, dim, kernel_size=1, bias=qkv_bias)
        self.k_conv = nn.Conv2d(dim, dim, kernel_size=1, bias=qkv_bias)
        self.v_conv = nn.Conv2d(dim, dim, kernel_size=1, bias=qkv_bias)
        
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Conv2d(dim, dim, kernel_size=1)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x_s2, x_s1):
        """
        Args:
            x_s2: Sentinel-2 features [B, C, H, W]
            x_s1: Sentinel-1 features [B, C, H, W]
        """
        B, C, H, W = x_s2.shape
        
        # S2作为Query，S1作为Key和Value
        q = self.q_conv(x_s2).reshape(B, self.num_heads, C // self.num_heads, H * W).permute(0, 1, 3, 2)
        k = self.k_conv(x_s1).reshape(B, self.num_heads, C // self.num_heads, H * W).permute(0, 1, 3, 2)
        v = self.v_conv(x_s1).reshape(B, self.num_heads, C // self.num_heads, H * W).permute(0, 1, 3, 2)

        q = torch.nn.functional.normalize(q, dim=-1)
        k = torch.nn.functional.normalize(k, dim=-1)

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, H * W, C).transpose(1, 2).reshape(B, C, H, W)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


class FeedForward(nn.Module):
    """前馈网络"""
    def __init__(self, dim, hidden_dim, drop=0.):
        super().__init__()
        self.net = nn.Sequential(
            nn.Conv2d(dim, hidden_dim, 1),
            nn.GELU(),
            nn.Dropout(drop),
            nn.Conv2d(hidden_dim, dim, 1),
            nn.Dropout(drop)
        )

    def forward(self, x):
        return self.net(x)


class TransformerBlock(nn.Module):
    """Transformer块，包含自注意力和前馈网络"""
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0., drop_path=0.):
        super().__init__()
        self.norm1 = LayerNorm(dim)
        self.attn = nn.MultiheadAttention(dim, num_heads, dropout=attn_drop, batch_first=True)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = LayerNorm(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = FeedForward(dim, mlp_hidden_dim, drop)

    def forward(self, x):
        B, C, H, W = x.shape
        
        # Self-attention
        shortcut = x
        x = self.norm1(x)
        x_flat = x.flatten(2).transpose(1, 2)  # [B, H*W, C]
        attn_out, _ = self.attn(x_flat, x_flat, x_flat)
        x = attn_out.transpose(1, 2).reshape(B, C, H, W)
        x = shortcut + self.drop_path(x)
        
        # FFN
        shortcut = x
        x = self.norm2(x)
        x = self.mlp(x)
        x = shortcut + self.drop_path(x)
        
        return x


class EarlyFusion(nn.Module):
    """早期融合：在输入层直接拼接"""
    def __init__(self, s2_channels, s1_channels, out_channels):
        super().__init__()
        self.conv = nn.Conv2d(s2_channels + s1_channels, out_channels, 3, 1, 1)
        self.norm = nn.BatchNorm2d(out_channels)
        self.act = nn.ReLU(inplace=True)

    def forward(self, s2_feat, s1_feat):
        fused = torch.cat([s2_feat, s1_feat], dim=1)
        fused = self.conv(fused)
        fused = self.norm(fused)
        fused = self.act(fused)
        return fused


class MiddleFusion(nn.Module):
    """中间融合：特征提取后融合"""
    def __init__(self, dim):
        super().__init__()
        self.conv1x1 = nn.Conv2d(dim * 2, dim, 1)
        self.norm = nn.BatchNorm2d(dim)
        self.act = nn.ReLU(inplace=True)

    def forward(self, s2_feat, s1_feat):
        fused = torch.cat([s2_feat, s1_feat], dim=1)
        fused = self.conv1x1(fused)
        fused = self.norm(fused)
        fused = self.act(fused)
        return fused


class CrossAttentionFusion(nn.Module):
    """跨注意力融合"""
    def __init__(self, dim, num_heads=8):
        super().__init__()
        self.cross_attn_s2_s1 = CrossAttention(dim, num_heads)
        self.cross_attn_s1_s2 = CrossAttention(dim, num_heads)
        self.fusion_conv = nn.Conv2d(dim * 3, dim, 1)
        self.norm = nn.BatchNorm2d(dim)
        self.act = nn.ReLU(inplace=True)

    def forward(self, s2_feat, s1_feat):
        # S2增强的特征（使用S1信息）
        s2_enhanced = self.cross_attn_s2_s1(s2_feat, s1_feat)
        # S1增强的特征（使用S2信息）
        s1_enhanced = self.cross_attn_s1_s2(s1_feat, s2_feat)
        
        # 融合原始特征和增强特征
        fused = torch.cat([s2_feat, s1_feat, s2_enhanced + s1_enhanced], dim=1)
        fused = self.fusion_conv(fused)
        fused = self.norm(fused)
        fused = self.act(fused)
        return fused


class MultiModalEncoder(nn.Module):
    """多模态编码器"""
    def __init__(self, s2_channels=4, s1_channels=2, hidden_dim=64, num_heads=8, num_blocks=4):
        super().__init__()
        
        # 单独的特征提取器
        self.s2_conv = nn.Sequential(
            nn.Conv2d(s2_channels, hidden_dim, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        )
        
        self.s1_conv = nn.Sequential(
            nn.Conv2d(s1_channels, hidden_dim, 3, 1, 1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True)
        )
        
        # Transformer编码器
        self.s2_blocks = nn.ModuleList([
            TransformerBlock(hidden_dim, num_heads) for _ in range(num_blocks)
        ])
        
        self.s1_blocks = nn.ModuleList([
            TransformerBlock(hidden_dim, num_heads) for _ in range(num_blocks)
        ])

    def forward(self, s2_data, s1_data):
        # 特征提取
        s2_feat = self.s2_conv(s2_data)
        s1_feat = self.s1_conv(s1_data)
        
        # Transformer编码
        for s2_block, s1_block in zip(self.s2_blocks, self.s1_blocks):
            s2_feat = s2_block(s2_feat)
            s1_feat = s1_block(s1_feat)
            
        return s2_feat, s1_feat


class MultiModalFusionNet(nn.Module):
    """多模态融合网络"""
    def __init__(self, 
                 s2_channels=4, 
                 s1_channels=2, 
                 hidden_dim=64, 
                 num_heads=8, 
                 num_blocks=4,
                 fusion_type='cross_attention'):
        super().__init__()
        
        self.fusion_type = fusion_type
        
        # 编码器
        self.encoder = MultiModalEncoder(s2_channels, s1_channels, hidden_dim, num_heads, num_blocks)
        
        # 融合模块
        if fusion_type == 'early':
            self.fusion = EarlyFusion(s2_channels, s1_channels, hidden_dim)
        elif fusion_type == 'middle':
            self.fusion = MiddleFusion(hidden_dim)
        elif fusion_type == 'cross_attention':
            self.fusion = CrossAttentionFusion(hidden_dim, num_heads)
        else:
            raise ValueError(f"不支持的融合类型: {fusion_type}")

    def forward(self, s2_data, s1_data):
        """
        Args:
            s2_data: Sentinel-2 数据 [B, C_s2, H, W]  
            s1_data: Sentinel-1 数据 [B, C_s1, H, W]
        Returns:
            fused_features: 融合后的特征 [B, hidden_dim, H, W]
        """
        if self.fusion_type == 'early':
            # 早期融合：直接在输入层拼接
            fused_feat = self.fusion(s2_data, s1_data)
            return fused_feat, None, None
        else:
            # 先分别编码
            s2_feat, s1_feat = self.encoder(s2_data, s1_data)
            
            # 然后融合
            fused_feat = self.fusion(s2_feat, s1_feat)
            
            return fused_feat, s2_feat, s1_feat


if __name__ == "__main__":
    # 测试网络
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model = MultiModalFusionNet(
        s2_channels=4, 
        s1_channels=2, 
        hidden_dim=64,
        fusion_type='cross_attention'
    ).to(device)
    
    # 模拟输入
    s2_input = torch.randn(2, 4, 128, 128).to(device)  # Sentinel-2
    s1_input = torch.randn(2, 2, 128, 128).to(device)  # Sentinel-1
    
    fused, s2_feat, s1_feat = model(s2_input, s1_input)
    
    print(f"S2输入形状: {s2_input.shape}")
    print(f"S1输入形状: {s1_input.shape}")
    print(f"融合特征形状: {fused.shape}")
    print(f"参数量: {sum(p.numel() for p in model.parameters())}") 