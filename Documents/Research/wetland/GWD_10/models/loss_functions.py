import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Optional


class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance"""
    def __init__(self, alpha=1, gamma=2, reduction='mean', ignore_index=255):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        """
        Args:
            inputs: predictions [B, C, H, W]
            targets: ground truth [B, H, W]
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none', ignore_index=self.ignore_index)
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class DiceLoss(nn.Module):
    """Dice Loss for segmentation"""
    def __init__(self, smooth=1e-6, ignore_index=255):
        super().__init__()
        self.smooth = smooth
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        """
        Args:
            inputs: predictions [B, C, H, W]
            targets: ground truth [B, H, W]
        """
        # Convert to one-hot
        num_classes = inputs.shape[1]
        targets_one_hot = F.one_hot(targets, num_classes).permute(0, 3, 1, 2).float()
        
        # Apply softmax to inputs
        inputs_soft = F.softmax(inputs, dim=1)
        
        # Flatten
        inputs_flat = inputs_soft.view(inputs_soft.shape[0], inputs_soft.shape[1], -1)
        targets_flat = targets_one_hot.view(targets_one_hot.shape[0], targets_one_hot.shape[1], -1)
        
        # Calculate dice coefficient
        intersection = (inputs_flat * targets_flat).sum(dim=2)
        dice = (2. * intersection + self.smooth) / (inputs_flat.sum(dim=2) + targets_flat.sum(dim=2) + self.smooth)
        
        # Return dice loss
        return 1 - dice.mean()


class LovaszSoftmaxLoss(nn.Module):
    """Lovasz Softmax Loss for multiclass segmentation"""
    def __init__(self, ignore_index=255):
        super().__init__()
        self.ignore_index = ignore_index

    def lovasz_grad(self, gt_sorted):
        """Compute gradient of the Lovasz extension w.r.t sorted errors"""
        gts = gt_sorted.sum()
        intersection = gts - gt_sorted.cumsum(0)
        union = gts + (1 - gt_sorted).cumsum(0)
        jaccard = 1. - intersection / union
        if len(jaccard) > 1:
            jaccard[1:] = jaccard[1:] - jaccard[:-1]
        return jaccard

    def lovasz_softmax_flat(self, probas, labels):
        """Multi-class Lovasz-Softmax loss"""
        if probas.numel() == 0:
            return probas * 0.
        
        C = probas.size(1)
        losses = []
        
        for c in range(C):
            fg = (labels == c).float()
            if fg.sum() == 0:
                continue
            errors = (1 - probas[:, c]) * fg + probas[:, c] * (1 - fg)
            errors_sorted, perm = torch.sort(errors, 0, descending=True)
            perm = perm.data
            fg_sorted = fg[perm]
            losses.append(torch.dot(errors_sorted, self.lovasz_grad(fg_sorted)))
        
        return torch.stack(losses).mean() if losses else probas.sum() * 0.

    def forward(self, inputs, targets):
        """
        Args:
            inputs: predictions [B, C, H, W]
            targets: ground truth [B, H, W]
        """
        probas = F.softmax(inputs, dim=1)
        
        # Flatten
        probas_flat = probas.view(-1, probas.shape[1])
        targets_flat = targets.view(-1)
        
        # Remove ignore index
        if self.ignore_index is not None:
            valid = targets_flat != self.ignore_index
            probas_flat = probas_flat[valid]
            targets_flat = targets_flat[valid]
        
        return self.lovasz_softmax_flat(probas_flat, targets_flat)


class ConsistencyLoss(nn.Module):
    """一致性损失，用于约束两个分类器的输出一致性"""
    def __init__(self, loss_type='mse', temperature=1.0):
        super().__init__()
        self.loss_type = loss_type
        self.temperature = temperature

    def forward(self, pred1, pred2):
        """
        Args:
            pred1: first prediction [B, C, H, W]
            pred2: second prediction [B, C, H, W]
        """
        if self.loss_type == 'mse':
            # MSE between softmax outputs
            prob1 = F.softmax(pred1 / self.temperature, dim=1)
            prob2 = F.softmax(pred2 / self.temperature, dim=1)
            return F.mse_loss(prob1, prob2)
        
        elif self.loss_type == 'kl':
            # KL divergence
            log_prob1 = F.log_softmax(pred1 / self.temperature, dim=1)
            prob2 = F.softmax(pred2 / self.temperature, dim=1)
            return F.kl_div(log_prob1, prob2, reduction='batchmean')
        
        elif self.loss_type == 'js':
            # Jensen-Shannon divergence
            prob1 = F.softmax(pred1 / self.temperature, dim=1)
            prob2 = F.softmax(pred2 / self.temperature, dim=1)
            m = 0.5 * (prob1 + prob2)
            kl1 = F.kl_div(F.log_softmax(pred1 / self.temperature, dim=1), m, reduction='batchmean')
            kl2 = F.kl_div(F.log_softmax(pred2 / self.temperature, dim=1), m, reduction='batchmean')
            return 0.5 * (kl1 + kl2)
        
        else:
            raise ValueError(f"不支持的一致性损失类型: {self.loss_type}")


class SegmentationLoss(nn.Module):
    """分割损失，用于点监督训练"""
    def __init__(self, 
                 loss_type='focal', 
                 focal_alpha=1.0, 
                 focal_gamma=2.0,
                 dice_weight=0.0,
                 ignore_index=255):
        super().__init__()
        self.loss_type = loss_type
        self.dice_weight = dice_weight
        
        if loss_type == 'cross_entropy':
            self.ce_loss = nn.CrossEntropyLoss(ignore_index=ignore_index)
        elif loss_type == 'focal':
            self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma, ignore_index=ignore_index)
        else:
            raise ValueError(f"不支持的分割损失类型: {loss_type}")
        
        if dice_weight > 0:
            self.dice_loss = DiceLoss(ignore_index=ignore_index)

    def forward(self, predictions, targets):
        """
        Args:
            predictions: model predictions [B, C, H, W]
            targets: ground truth labels [B, H, W]
        """
        if self.loss_type == 'cross_entropy':
            loss = self.ce_loss(predictions, targets)
        elif self.loss_type == 'focal':
            loss = self.focal_loss(predictions, targets)
        
        if self.dice_weight > 0:
            dice_loss = self.dice_loss(predictions, targets)
            loss = loss + self.dice_weight * dice_loss
        
        return loss


class ExpansionLoss(nn.Module):
    """扩展损失，用于扩展标签的训练"""
    def __init__(self, loss_type='lovasz', weight=None, ignore_index=255):
        super().__init__()
        self.loss_type = loss_type
        self.ignore_index = ignore_index
        
        if loss_type == 'lovasz':
            self.loss_fn = LovaszSoftmaxLoss(ignore_index=ignore_index)
        elif loss_type == 'cross_entropy':
            self.loss_fn = nn.CrossEntropyLoss(weight=weight, ignore_index=ignore_index)
        elif loss_type == 'focal':
            self.loss_fn = FocalLoss(ignore_index=ignore_index)
        else:
            raise ValueError(f"不支持的扩展损失类型: {loss_type}")

    def forward(self, predictions, expanded_labels):
        """
        Args:
            predictions: model predictions [B, C, H, W]
            expanded_labels: expanded labels [B, H, W]
        """
        return self.loss_fn(predictions, expanded_labels)


class WeightedLoss(nn.Module):
    """加权损失，动态调整各损失项的权重"""
    def __init__(self, weights=None, adaptive=False):
        super().__init__()
        default_weights = {'segmentation': 1.0, 'expansion': 1.0, 'consistency': 1.0}
        self.weights = weights or default_weights
        self.adaptive = adaptive
        
        # 动态调整损失历史记录大小
        num_losses = len(self.weights)
        self.register_buffer('loss_history', torch.zeros(num_losses, 100))
        self.step = 0
        self.loss_keys = list(self.weights.keys())

    def update_weights(self, losses):
        """动态更新权重"""
        if not self.adaptive:
            return
        
        # 记录损失历史
        idx = self.step % 100
        for i, key in enumerate(self.loss_keys):
            if key in losses:
                self.loss_history[i, idx] = losses[key].item()
        
        if self.step > 100:
            # 计算最近100步的平均损失
            recent_losses = self.loss_history.mean(dim=1)
            
            # 根据损失大小调整权重（损失大的权重小）
            total_loss = recent_losses.sum()
            for i, key in enumerate(self.loss_keys):
                if recent_losses[i] > 0:
                    self.weights[key] = (total_loss / recent_losses[i]).item()
            
            # 归一化权重
            total_weight = sum(self.weights.values())
            if total_weight > 0:
                for key in self.weights:
                    self.weights[key] /= total_weight
        
        self.step += 1

    def forward(self, **losses):
        """
        Args:
            **losses: keyword arguments with loss values
        """
        # 动态更新权重
        self.update_weights(losses)
        
        # 计算加权总损失
        total_loss = torch.tensor(0.0, device=list(losses.values())[0].device)
        for key, loss in losses.items():
            if key in self.weights:
                total_loss += self.weights[key] * loss
        
        return total_loss, self.weights


class CorrelationLoss(nn.Module):
    """相关性损失，用于MMIF-CDDFuse"""
    def __init__(self, base_weight=1.0, detail_weight=1.0):
        super().__init__()
        self.base_weight = base_weight
        self.detail_weight = detail_weight

    def forward(self, base_feature_s2, base_feature_s1, detail_feature_s2, detail_feature_s1):
        """
        计算相关性损失：使基础特征相关，细节特征不相关
        Args:
            base_feature_s2: S2基础特征 [B, C, H, W]
            base_feature_s1: S1基础特征 [B, C, H, W]  
            detail_feature_s2: S2细节特征 [B, C, H, W]
            detail_feature_s1: S1细节特征 [B, C, H, W]
        """
        # 基础特征相关性损失（应该相关）
        base_s2_flat = base_feature_s2.view(base_feature_s2.size(0), -1)
        base_s1_flat = base_feature_s1.view(base_feature_s1.size(0), -1)
        
        # 计算余弦相似度
        base_cosine_sim = F.cosine_similarity(base_s2_flat, base_s1_flat, dim=1)
        base_correlation_loss = 1 - base_cosine_sim.mean()  # 最大化相关性
        
        # 细节特征去相关性损失（应该不相关）
        detail_s2_flat = detail_feature_s2.view(detail_feature_s2.size(0), -1)
        detail_s1_flat = detail_feature_s1.view(detail_feature_s1.size(0), -1)
        
        # 计算余弦相似度
        detail_cosine_sim = F.cosine_similarity(detail_s2_flat, detail_s1_flat, dim=1)
        detail_decorrelation_loss = torch.abs(detail_cosine_sim).mean()  # 最小化相关性
        
        total_loss = (self.base_weight * base_correlation_loss + 
                     self.detail_weight * detail_decorrelation_loss)
        
        return total_loss, base_correlation_loss, detail_decorrelation_loss


class CompositeLoss(nn.Module):
    """综合损失函数"""
    def __init__(self, config):
        super().__init__()
        
        # 从配置中获取损失权重
        loss_weights = config.get('training', {}).get('loss_weights', {})
        fusion_type = config.get('model', {}).get('fusion', {}).get('fusion_type', 'cross_attention')
        
        # 分割损失
        self.seg_loss = SegmentationLoss(
            loss_type='focal',
            focal_alpha=1.0,
            focal_gamma=2.0,
            dice_weight=0.5
        )
        
        # 扩展损失
        self.exp_loss = ExpansionLoss(loss_type='lovasz')
        
        # 一致性损失
        self.con_loss = ConsistencyLoss(loss_type='mse', temperature=4.0)
        
        # MMIF相关性损失
        self.use_correlation_loss = (fusion_type == 'mmif_cddfuse')
        if self.use_correlation_loss:
            self.corr_loss = CorrelationLoss(base_weight=1.0, detail_weight=1.0)
        
        # 加权损失权重
        weights = loss_weights.copy()
        if self.use_correlation_loss:
            weights['correlation'] = weights.get('correlation', 0.5)
        
        self.weighted_loss = WeightedLoss(
            weights=weights,
            adaptive=True
        )

    def forward(self, predictions, targets):
        """
        Args:
            predictions: dict containing model predictions
            targets: dict containing ground truth data
        Returns:
            total_loss: total weighted loss
            loss_dict: individual loss components
        """
        loss_dict = {}
        device = predictions['base_pred'].device
        
        # 分割损失（点监督）
        if 'base_pred' in predictions and 'point_labels' in targets:
            seg_loss = self.seg_loss(predictions['base_pred'], targets['point_labels'])
            loss_dict['segmentation_loss'] = seg_loss
        else:
            seg_loss = torch.tensor(0.0, device=device)
            loss_dict['segmentation_loss'] = seg_loss
        
        # 扩展损失
        if 'expanded_pred' in predictions and 'expanded_labels' in predictions:
            exp_loss = self.exp_loss(predictions['expanded_pred'], predictions['expanded_labels'])
            loss_dict['expansion_loss'] = exp_loss
        else:
            exp_loss = torch.tensor(0.0, device=device)
            loss_dict['expansion_loss'] = exp_loss
        
        # 一致性损失
        if 'base_pred' in predictions and 'expanded_pred' in predictions:
            con_loss = self.con_loss(predictions['base_pred'], predictions['expanded_pred'])
            loss_dict['consistency_loss'] = con_loss
        else:
            con_loss = torch.tensor(0.0, device=device)
            loss_dict['consistency_loss'] = con_loss
        
        # MMIF相关性损失
        if self.use_correlation_loss:
            if ('base_feature_s2' in predictions and 'base_feature_s1' in predictions and
                'detail_feature_s2' in predictions and 'detail_feature_s1' in predictions):
                corr_loss, base_corr, detail_decorr = self.corr_loss(
                    predictions['base_feature_s2'], predictions['base_feature_s1'],
                    predictions['detail_feature_s2'], predictions['detail_feature_s1']
                )
                loss_dict['correlation_loss'] = corr_loss
                loss_dict['base_correlation'] = base_corr
                loss_dict['detail_decorrelation'] = detail_decorr
            else:
                corr_loss = torch.tensor(0.0, device=device)
                loss_dict['correlation_loss'] = corr_loss
        else:
            corr_loss = torch.tensor(0.0, device=device)
        
        # 准备损失字典用于加权计算
        losses_for_weighting = {
            'segmentation': seg_loss,
            'expansion': exp_loss,
            'consistency': con_loss
        }
        
        if self.use_correlation_loss:
            losses_for_weighting['correlation'] = corr_loss
        
        # 计算加权总损失
        total_loss, weights = self.weighted_loss(**losses_for_weighting)
        loss_dict['total_loss'] = total_loss
        loss_dict['weights'] = weights
        
        return total_loss, loss_dict


def create_loss_function(config):
    """根据配置创建损失函数"""
    return CompositeLoss(config)


if __name__ == "__main__":
    # 测试损失函数
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 模拟配置
    config = {
        'training': {
            'loss_weights': {
                'segmentation': 1.0,
                'expansion': 1.0,
                'consistency': 0.5
            }
        }
    }
    
    # 创建损失函数
    loss_fn = create_loss_function(config)
    
    # 模拟预测和目标
    batch_size, num_classes, height, width = 2, 2, 128, 128
    
    predictions = {
        'base_pred': torch.randn(batch_size, num_classes, height, width).to(device),
        'expanded_pred': torch.randn(batch_size, num_classes, height, width).to(device),
        'expanded_labels': torch.randint(0, num_classes, (batch_size, height, width)).to(device)
    }
    
    targets = {
        'point_labels': torch.randint(0, num_classes, (batch_size, height, width)).to(device)
    }
    
    # 计算损失
    total_loss, loss_dict = loss_fn(predictions, targets)
    
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.item():.4f}")
        else:
            print(f"{key}: {value}") 