#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于CRGNet的弱监督分割网络
Consistency-Regularized Region-Growing Network
适配多模态遥感数据
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import skimage.morphology as morph
from typing import Tuple, Dict, Optional
from .fusion_net import MultiModalFusionNet
from .mmif_fusion_net import MMIFFusionNet


class DilatedClassifier(nn.Module):
    """空洞卷积分类器模块"""
    def __init__(self, in_channels, num_classes, dilation_rates=[6, 12, 18, 24]):
        super(DilatedClassifier, self).__init__()
        self.conv_list = nn.ModuleList()
        
        for dilation in dilation_rates:
            self.conv_list.append(
                nn.Conv2d(in_channels, num_classes, kernel_size=3, 
                         stride=1, padding=dilation, dilation=dilation, bias=True)
            )
        
        # 权重初始化
        for conv in self.conv_list:
            nn.init.normal_(conv.weight, 0, 0.01)
            if conv.bias is not None:
                nn.init.constant_(conv.bias, 0)

    def forward(self, x):
        out = self.conv_list[0](x)
        for i in range(1, len(self.conv_list)):
            out += self.conv_list[i](x)
        return out


class CRGNetBackbone(nn.Module):
    """CRGNet的双分类器主干网络"""
    def __init__(self, fusion_channels, num_classes):
        super(CRGNetBackbone, self).__init__()
        
        # 特征提取网络
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(fusion_channels, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            # 全卷积层
            nn.Conv2d(512, 1024, kernel_size=3, padding=4, dilation=4),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, kernel_size=3, padding=4, dilation=4),
            nn.ReLU(inplace=True)
        )
        
        # 基础分类器
        self.base_classifier = DilatedClassifier(1024, num_classes)
        
        # 扩展分类器
        self.expanded_classifier = DilatedClassifier(1024, num_classes)

    def forward(self, x):
        features = self.feature_extractor(x)
        base_pred = self.base_classifier(features)
        expanded_pred = self.expanded_classifier(features)
        return base_pred, expanded_pred


class AdvancedRegionGrowing:
    """高级区域增长算法，基于CRGNet"""
    def __init__(self, 
                 confidence_threshold=0.95,
                 max_iterations=10,
                 morphology_kernel=3,
                 boundary_removal_kernel=8):
        self.tau = confidence_threshold
        self.max_iterations = max_iterations
        self.se = morph.square(morphology_kernel)
        self.se_boundary = morph.square(boundary_removal_kernel)
        
        # 8连通邻域
        self.delta_r = np.array([-1, 0, 1, -1, 1, -1, 0, 1])
        self.delta_c = np.array([1, 1, 1, 0, 0, -1, -1, -1])

    def grow_regions(self, predictions, confidences, point_labels, current_iter=0, total_iters=1000):
        """
        动态区域增长算法
        Args:
            predictions: 预测类别 [B, H, W]
            confidences: 预测置信度 [B, H, W]
            point_labels: 点标签 [B, H, W]
            current_iter: 当前训练迭代
            total_iters: 总训练迭代数
        Returns:
            expanded_labels: 扩展后的标签 [B, H, W]
        """
        batch_size, height, width = predictions.shape
        expanded_labels = point_labels.copy()
        
        # 动态调整最大迭代次数
        max_iter = int(self.max_iterations * ((float(current_iter) / total_iters) ** 0.9))
        max_iter = max(1, max_iter)
        
        for b in range(batch_size):
            pred_b = predictions[b]
            conf_b = confidences[b]
            label_b = expanded_labels[b].copy()
            
            # 区域增长迭代
            for iteration in range(max_iter):
                updated = False
                
                # 找到标签边界像素
                label_mask = (label_b != 255).astype(np.uint8)
                erosion = morph.erosion(label_mask, self.se)
                boundary = label_mask - erosion
                
                boundary_coords = np.where(boundary > 0)
                
                for i in range(len(boundary_coords[0])):
                    r, c = boundary_coords[0][i], boundary_coords[1][i]
                    current_label = label_b[r, c]
                    
                    # 检查8连通邻域
                    for j in range(len(self.delta_r)):
                        nr = r + self.delta_r[j]
                        nc = c + self.delta_c[j]
                        
                        # 边界检查
                        if 0 <= nr < height and 0 <= nc < width:
                            if label_b[nr, nc] == 255:  # 未标记像素
                                # 检查预测一致性和置信度
                                pred_neighbor = pred_b[nr, nc]
                                conf_neighbor = conf_b[nr, nc]
                                
                                if (pred_neighbor == current_label and 
                                    conf_neighbor > self.tau):
                                    label_b[nr, nc] = current_label
                                    updated = True
                
                if not updated:
                    break
            
            # 应用形态学操作去除边界噪声
            expanded_labels[b] = morph.dilation(label_b, self.se_boundary)
        
        return expanded_labels


class CRGNet(nn.Module):
    """
    Consistency-Regularized Region-Growing Network
    适配多模态遥感数据的弱监督分割
    """
    def __init__(self,
                 s2_channels=4,
                 s1_channels=2,
                 num_classes=4,
                 hidden_dim=64,
                 fusion_type='mmif_cddfuse',
                 fusion_config=None,
                 region_growing_config=None):
        super(CRGNet, self).__init__()
        
        self.num_classes = num_classes
        self.fusion_type = fusion_type
        
        # 多模态融合网络
        if fusion_type == 'mmif_cddfuse':
            if fusion_config is None:
                fusion_config = {
                    'num_blocks': [4, 4],
                    'heads': [8, 8, 8],
                    'ffn_expansion_factor': 2,
                    'bias': False
                }
            self.fusion_net = MMIFFusionNet(
                s2_channels=s2_channels,
                s1_channels=s1_channels,
                hidden_dim=hidden_dim,
                **fusion_config
            )
        else:
            self.fusion_net = MultiModalFusionNet(
                s2_channels=s2_channels,
                s1_channels=s1_channels,
                hidden_dim=hidden_dim,
                fusion_type=fusion_type
            )
        
        # CRGNet双分类器
        self.crgnet_backbone = CRGNetBackbone(hidden_dim, num_classes)
        
        # 高级区域增长算法
        if region_growing_config is None:
            region_growing_config = {}
        self.region_growing = AdvancedRegionGrowing(**region_growing_config)

    def forward(self, s2_data, s1_data, point_labels=None, training=True, current_iter=0, total_iters=1000):
        """
        前向传播
        Args:
            s2_data: Sentinel-2数据 [B, C_s2, H, W]
            s1_data: Sentinel-1数据 [B, C_s1, H, W]
            point_labels: 点标签 [B, H, W]
            training: 是否为训练模式
            current_iter: 当前训练迭代
            total_iters: 总训练迭代数
        Returns:
            results: 包含预测结果和扩展标签的字典
        """
        # 多模态特征融合
        if self.fusion_type == 'mmif_cddfuse':
            fused_features, base_feature, detail_feature = self.fusion_net(s2_data, s1_data)
        else:
            fused_features, _, _ = self.fusion_net(s2_data, s1_data)
        
        # CRGNet双分类器预测
        base_pred, expanded_pred = self.crgnet_backbone(fused_features)
        
        results = {
            'base_pred': base_pred,
            'expanded_pred': expanded_pred
        }
        
        # 训练时进行区域增长
        if training and point_labels is not None:
            # 获取基础分类器的预测类别和置信度
            base_prob = F.softmax(base_pred, dim=1)
            base_conf, base_class = torch.max(base_prob, dim=1)
            
            # 转换为numpy进行区域增长
            base_class_np = base_class.detach().cpu().numpy()
            base_conf_np = base_conf.detach().cpu().numpy()
            point_labels_np = point_labels.detach().cpu().numpy()
            
            # 动态区域增长
            expanded_labels_np = self.region_growing.grow_regions(
                base_class_np, base_conf_np, point_labels_np, 
                current_iter, total_iters
            )
            
            # 转换回tensor
            expanded_labels = torch.from_numpy(expanded_labels_np).to(s2_data.device)
            results['expanded_labels'] = expanded_labels
        
        return results

    def generate_pseudo_labels(self, s2_data, s1_data, point_labels):
        """
        生成伪标签用于自训练
        Args:
            s2_data: Sentinel-2数据
            s1_data: Sentinel-1数据  
            point_labels: 点标签
        Returns:
            pseudo_labels: 生成的伪标签
        """
        self.eval()
        with torch.no_grad():
            results = self.forward(s2_data, s1_data, point_labels, training=True)
            
            # 融合两个分类器的预测
            base_prob = F.softmax(results['base_pred'], dim=1)
            expanded_prob = F.softmax(results['expanded_pred'], dim=1)
            fused_prob = (base_prob + expanded_prob) / 2.0
            
            # 生成伪标签
            pseudo_labels = torch.argmax(fused_prob, dim=1)
            
        return pseudo_labels


class CRGNetLoss(nn.Module):
    """CRGNet的复合损失函数"""
    def __init__(self, 
                 lambda_consistency=1.0,
                 ignore_index=255):
        super(CRGNetLoss, self).__init__()
        self.lambda_consistency = lambda_consistency
        self.ignore_index = ignore_index
        
        # 分割损失（基础分类器）
        self.segmentation_loss = nn.CrossEntropyLoss(ignore_index=ignore_index)
        
        # 扩展损失（Lovász-Softmax）
        self.expansion_loss = self._lovasz_softmax
        
        # 一致性损失
        self.consistency_loss = nn.MSELoss()

    def _lovasz_softmax(self, probas, labels):
        """多分类Lovász-Softmax损失"""
        if probas.numel() == 0:
            return probas * 0.
        
        C = probas.shape[1]  # 类别数
        losses = []
        
        # 为每个类别计算Lovász损失
        for c in range(C):
            # 创建二分类标签：当前类别 vs 其他
            fg = (labels == c).float()  # 前景（当前类别）
            if fg.sum() == 0:  # 如果没有该类别的样本，跳过
                continue
            
            # 获取当前类别的概率
            class_pred = probas[:, c]
            
            # 计算该类别的Lovász损失
            errors = (1. - class_pred * fg).clamp(min=0)
            errors_flat = errors.view(-1)
            fg_flat = fg.view(-1)
            
            # 按错误排序
            errors_sorted, perm = torch.sort(errors_flat, dim=0, descending=True)
            fg_sorted = fg_flat[perm]
            
            # 计算Lovász梯度
            grad = self._lovasz_grad(fg_sorted)
            loss = torch.dot(errors_sorted, grad)
            losses.append(loss)
        
        if len(losses) == 0:
            return torch.tensor(0., device=probas.device, requires_grad=True)
        
        return sum(losses) / len(losses)

    def _lovasz_grad(self, gt_sorted):
        """计算Lovász梯度"""
        p = len(gt_sorted)
        gts = gt_sorted.sum()
        intersection = gts - gt_sorted.float().cumsum(0)
        union = gts + (1 - gt_sorted).float().cumsum(0)
        jaccard = 1. - intersection / union
        if p > 1:
            jaccard[1:p] = jaccard[1:p] - jaccard[0:p-1]
        return jaccard

    def forward(self, predictions, targets):
        """
        计算CRGNet的复合损失
        Args:
            predictions: 模型预测结果字典
            targets: 目标标签字典
        Returns:
            total_loss: 总损失
            loss_dict: 各损失项详情
        """
        device = predictions['base_pred'].device
        loss_dict = {}
        
        # 分割损失（基础分类器 + 点标签）
        if 'point_labels' in targets:
            seg_loss = self.segmentation_loss(predictions['base_pred'], targets['point_labels'])
            loss_dict['segmentation_loss'] = seg_loss
        else:
            seg_loss = torch.tensor(0.0, device=device)
            loss_dict['segmentation_loss'] = seg_loss
        
        # 扩展损失（扩展分类器 + 扩展标签）
        if 'expanded_labels' in predictions:
            exp_loss = self.expansion_loss(
                F.softmax(predictions['expanded_pred'], dim=1),
                predictions['expanded_labels']
            )
            loss_dict['expansion_loss'] = exp_loss
        else:
            exp_loss = torch.tensor(0.0, device=device)
            loss_dict['expansion_loss'] = exp_loss
        
        # 一致性损失（两个分类器的输出一致性）
        base_prob = F.softmax(predictions['base_pred'], dim=1)
        expanded_prob = F.softmax(predictions['expanded_pred'], dim=1)
        con_loss = self.consistency_loss(base_prob, expanded_prob)
        loss_dict['consistency_loss'] = con_loss
        
        # 总损失
        total_loss = seg_loss + exp_loss + self.lambda_consistency * con_loss
        loss_dict['total_loss'] = total_loss
        
        return total_loss, loss_dict


def create_crgnet(config):
    """根据配置创建CRGNet"""
    model_config = config['model']
    fusion_config = model_config['fusion']
    
    # 为MMIF融合网络准备配置参数
    mmif_config = None
    if fusion_config['fusion_type'] == 'mmif_cddfuse':
        mmif_config = {
            'num_blocks': fusion_config.get('num_blocks', [4, 4]),
            'heads': fusion_config.get('heads', [8, 8, 8]),
            'ffn_expansion_factor': fusion_config.get('ffn_expansion_factor', 2),
            'bias': fusion_config.get('bias', False)
        }
    
    # 区域增长配置
    region_growing_config = {
        'confidence_threshold': fusion_config.get('confidence_threshold', 0.95),
        'max_iterations': fusion_config.get('max_iterations', 10),
        'morphology_kernel': fusion_config.get('morphology_kernel', 3),
        'boundary_removal_kernel': fusion_config.get('boundary_removal_kernel', 8)
    }
    
    return CRGNet(
        s2_channels=fusion_config['s2_channels'],
        s1_channels=fusion_config['s1_channels'],
        num_classes=config['data']['num_classes'],
        hidden_dim=fusion_config['hidden_dim'],
        fusion_type=fusion_config['fusion_type'],
        fusion_config=mmif_config,
        region_growing_config=region_growing_config
    )


def create_crgnet_loss(config):
    """创建CRGNet损失函数"""
    training_config = config.get('training', {})
    loss_weights = training_config.get('loss_weights', {})
    
    return CRGNetLoss(
        lambda_consistency=loss_weights.get('consistency', 1.0),
        ignore_index=config.get('data', {}).get('ignore_index', 255)
    )


if __name__ == "__main__":
    # 测试CRGNet
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建模型
    config = {
        'data': {'num_classes': 4},
        'model': {
            'fusion': {
                'fusion_type': 'mmif_cddfuse',
                's2_channels': 4,
                's1_channels': 2,
                'hidden_dim': 64,
                'num_blocks': [2, 2],
                'heads': [4, 4, 4],
                'ffn_expansion_factor': 2,
                'bias': False
            }
        },
        'training': {
            'loss_weights': {
                'consistency': 1.0
            }
        }
    }
    
    model = create_crgnet(config).to(device)
    criterion = create_crgnet_loss(config)
    
    print(f"CRGNet参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 模拟输入
    batch_size = 2
    s2_input = torch.randn(batch_size, 4, 128, 128).to(device)
    s1_input = torch.randn(batch_size, 2, 128, 128).to(device)
    point_labels = torch.randint(0, 4, (batch_size, 128, 128)).to(device)
    
    # 设置大部分像素为未标记
    mask = torch.rand(batch_size, 128, 128) > 0.02
    point_labels[mask] = 255
    
    # 前向传播
    model.train()
    results = model(s2_input, s1_input, point_labels, training=True)
    
    print("模型输出:")
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")
    
    # 损失计算
    targets = {'point_labels': point_labels}
    total_loss, loss_dict = criterion(results, targets)
    
    print(f"\n损失计算:")
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.item():.4f}")
    
    print("✓ CRGNet测试通过") 