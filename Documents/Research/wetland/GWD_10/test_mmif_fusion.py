#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MMIF-CDDFuse融合网络的功能
"""

import torch
import torch.nn.functional as F
import yaml
import numpy as np
import matplotlib.pyplot as plt
from models.mmif_fusion_net import MMIFFusionNet
from models.weakly_net import create_weakly_segmentation_net
from models.loss_functions import create_loss_function


def test_mmif_fusion_basic():
    """测试MMIF融合网络的基本功能"""
    print("=" * 60)
    print("测试MMIF-CDDFuse融合网络基本功能")
    print("=" * 60)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建网络
    model = MMIFFusionNet(
        s2_channels=4,
        s1_channels=2,
        hidden_dim=64,
        num_blocks=[4, 4],
        heads=[8, 8, 8],
        ffn_expansion_factor=2,
        bias=False
    ).to(device)
    
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 模拟输入
    batch_size = 2
    height, width = 128, 128
    
    s2_input = torch.randn(batch_size, 4, height, width).to(device)
    s1_input = torch.randn(batch_size, 2, height, width).to(device)
    
    print(f"\n输入形状:")
    print(f"Sentinel-2: {s2_input.shape}")
    print(f"Sentinel-1: {s1_input.shape}")
    
    # 前向传播
    with torch.no_grad():
        fused_features, base_feature, detail_feature = model(s2_input, s1_input)
    
    print(f"\n输出形状:")
    print(f"融合特征: {fused_features.shape}")
    print(f"基础特征: {base_feature.shape}")
    print(f"细节特征: {detail_feature.shape}")
    
    # 测试相关性损失
    print("\n测试相关性损失...")
    
    # 分别获取S2和S1的特征
    with torch.no_grad():
        s2_fused, s2_base, s2_detail = model(s2_input, torch.zeros_like(s1_input))
        s1_fused, s1_base, s1_detail = model(torch.zeros_like(s2_input), s1_input)
    
    # 计算相关性损失
    correlation_loss = model.get_correlation_loss(s2_base, s1_base, s2_detail, s1_detail)
    print(f"相关性损失: {correlation_loss.item():.4f}")
    
    print("✓ MMIF融合网络基本功能测试通过")


def test_weakly_net_with_mmif():
    """测试集成MMIF融合的弱监督分割网络"""
    print("\n" + "=" * 60)
    print("测试集成MMIF融合的弱监督分割网络")
    print("=" * 60)
    
    # 创建配置
    config = {
        'data': {
            'num_classes': 2,
            'input_size': [128, 128]
        },
        'model': {
            'fusion': {
                'fusion_type': 'mmif_cddfuse',
                's2_channels': 4,
                's1_channels': 2,
                'hidden_dim': 64,
                'num_blocks': [4, 4],
                'heads': [8, 8, 8],
                'ffn_expansion_factor': 2,
                'bias': False
            },
            'weakly_supervised': {
                'dual_classifier': True,
                'region_growing': {
                    'max_iterations': 5,
                    'confidence_threshold': 0.9,
                    'neighbor_size': 8,
                    'morphology_kernel': 3
                }
            }
        },
        'training': {
            'loss_weights': {
                'segmentation': 1.0,
                'expansion': 1.0,
                'consistency': 0.5,
                'correlation': 0.5
            }
        }
    }
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建模型
    model = create_weakly_segmentation_net(config).to(device)
    print(f"弱监督分割网络参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建损失函数
    criterion = create_loss_function(config)
    
    # 模拟输入
    batch_size = 2
    height, width = 128, 128
    
    s2_input = torch.randn(batch_size, 4, height, width).to(device)
    s1_input = torch.randn(batch_size, 2, height, width).to(device)
    point_labels = torch.randint(0, 2, (batch_size, height, width)).to(device)
    
    # 将大部分标签设为未标记 (255)
    mask = torch.rand(batch_size, height, width) > 0.01  # 只有1%的像素有标签
    point_labels[mask] = 255
    
    print(f"\n输入数据:")
    print(f"Sentinel-2: {s2_input.shape}")
    print(f"Sentinel-1: {s1_input.shape}")
    print(f"点标签: {point_labels.shape}")
    print(f"有标签像素比例: {(point_labels != 255).float().mean():.4f}")
    
    # 前向传播
    model.train()
    results = model(s2_input, s1_input, point_labels, training=True)
    
    print(f"\n模型输出:")
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")
    
    # 添加MMIF相关特征进行损失计算
    with torch.no_grad():
        s2_fused, s2_base, s2_detail = model.fusion_net(s2_input, torch.zeros_like(s1_input))
        s1_fused, s1_base, s1_detail = model.fusion_net(torch.zeros_like(s2_input), s1_input)
        
        results['base_feature_s2'] = s2_base
        results['base_feature_s1'] = s1_base
        results['detail_feature_s2'] = s2_detail
        results['detail_feature_s1'] = s1_detail
    
    # 计算损失
    targets = {'point_labels': point_labels}
    total_loss, loss_dict = criterion(results, targets)
    
    print(f"\n损失计算:")
    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.item():.4f}")
        else:
            print(f"{key}: {value}")
    
    # 反向传播测试
    total_loss.backward()
    print("✓ 反向传播测试通过")
    
    print("✓ 集成MMIF融合的弱监督分割网络测试通过")


def test_feature_visualization():
    """测试特征可视化"""
    print("\n" + "=" * 60)
    print("测试特征可视化")
    print("=" * 60)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 创建模型
    model = MMIFFusionNet(
        s2_channels=4,
        s1_channels=2,
        hidden_dim=64
    ).to(device)
    
    model.eval()
    
    # 模拟输入
    s2_input = torch.randn(1, 4, 128, 128).to(device)
    s1_input = torch.randn(1, 2, 128, 128).to(device)
    
    with torch.no_grad():
        # 联合处理
        fused_features, base_feature, detail_feature = model(s2_input, s1_input)
        
        # 分别处理
        s2_fused, s2_base, s2_detail = model(s2_input, torch.zeros_like(s1_input))
        s1_fused, s1_base, s1_detail = model(torch.zeros_like(s2_input), s1_input)
    
    # 计算特征统计
    print(f"联合处理:")
    print(f"  融合特征均值: {fused_features.mean().item():.4f}, 标准差: {fused_features.std().item():.4f}")
    print(f"  基础特征均值: {base_feature.mean().item():.4f}, 标准差: {base_feature.std().item():.4f}")
    print(f"  细节特征均值: {detail_feature.mean().item():.4f}, 标准差: {detail_feature.std().item():.4f}")
    
    print(f"\nS2单独处理:")
    print(f"  基础特征均值: {s2_base.mean().item():.4f}, 标准差: {s2_base.std().item():.4f}")
    print(f"  细节特征均值: {s2_detail.mean().item():.4f}, 标准差: {s2_detail.std().item():.4f}")
    
    print(f"\nS1单独处理:")
    print(f"  基础特征均值: {s1_base.mean().item():.4f}, 标准差: {s1_base.std().item():.4f}")
    print(f"  细节特征均值: {s1_detail.mean().item():.4f}, 标准差: {s1_detail.std().item():.4f}")
    
    # 计算相关性
    def compute_correlation(feat1, feat2):
        feat1_flat = feat1.view(-1)
        feat2_flat = feat2.view(-1)
        return F.cosine_similarity(feat1_flat.unsqueeze(0), feat2_flat.unsqueeze(0)).item()
    
    base_correlation = compute_correlation(s2_base, s1_base)
    detail_correlation = compute_correlation(s2_detail, s1_detail)
    
    print(f"\n特征相关性:")
    print(f"  基础特征相关性: {base_correlation:.4f}")
    print(f"  细节特征相关性: {detail_correlation:.4f}")
    
    print("✓ 特征可视化测试通过")


def main():
    """主测试函数"""
    print("MMIF-CDDFuse融合网络测试")
    print("=" * 80)
    
    try:
        # 基本功能测试
        test_mmif_fusion_basic()
        
        # 集成测试
        test_weakly_net_with_mmif()
        
        # 特征可视化测试
        test_feature_visualization()
        
        print("\n" + "=" * 80)
        print("🎉 所有测试通过！MMIF-CDDFuse融合网络集成成功！")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 