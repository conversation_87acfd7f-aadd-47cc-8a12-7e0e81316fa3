# 湿地遥感影像分割研究需求文档

## 一、研究背景与动机

湿地在生态系统中发挥着重要作用。然而，受限于遥感数据的时空复杂性及标注成本，进行高精度的湿地分割仍面临挑战。现有方法多依赖全监督像素级标注，标注成本高、泛化能力差。

考虑到 Sentinel-2（10m，多光谱）和 Sentinel-1（10m，SAR）的互补特性，以及现有点样本数据，本研究旨在构建一个融合多源遥感数据的**弱监督深度学习分割框架**，以实现高质量湿地提取。

## 二、研究目标

- 构建一个多模态遥感影像融合模型，联合利用 Sentinel-2 和 Sentinel-1。
- 设计支持点样本训练的弱监督机制，避免依赖像素级标签。
- 输出高分辨率的湿地分割图。
- 验证模型在不同区域的迁移能力和鲁棒性。

## 三、输入数据说明

| 数据类型   | 来源             | 分辨率 | 内容                     | 格式           |
|------------|------------------|--------|--------------------------|----------------|
| Sentinel-2 | ESA Copernicus   | 10m    | B2, B3, B4, B8 等多光谱   | GeoTIFF        |
| Sentinel-1 | ESA Copernicus   | 10m    | VV/VH 极化后反射率等     | GeoTIFF        |
| 点样本     | 实地调查或历史样本 | 点状  | 湿地/非湿地类别标签     | Shapefile/CSV  |

> 每个样本点需与对应影像配准，可用于提取 Patch（如 128×128）。

## 四、方法需求

### 4.1 数据预处理模块

### 4.2 多模态数据融合模块

- 设计支持多模态融合的深度学习结构，包括：
  - Early fusion：输入层级别拼接；
  - Middle fusion：特征提取后中间融合；
  - Cross-attention 融合机制。
- 具体的融合方法我希望参考MMIF-CDDFuse这个文件夹
- 融合方式可配置并对比效果。

### 4.3 弱监督分割模块

- 支持点监督训练，候选策略包括但不限于：
- 具体的点监督训练方法我希望参考CRGNet这个文件夹
- 引入一致性约束与伪标签自训练机制。

### 4.4 损失函数设计

- 点位置监督损失（如 focal loss）；
- 区域平滑/一致性损失（optional）；
- 类别平衡损失；
- 多阶段伪标签更新与自蒸馏 loss。

## 五、输出结果

- 湿地分割结果图（GeoTIFF或者png, 10m resolution）。
- 精度评估报告，包括：
  - mIoU
  - F1-score
  - Precision / Recall
- 可视化图（类别热图、注意力图等）

## 六、扩展方向（可选）

- 加入DEM数据，支持三种模态（S-2， S-1， DEM）的输入和融合
- 加入时间序列信息，支持多时相输入和单时相输出（参考:[https://github.com/VSainteuf/utae-paps]）。

## 七、评估指标

| 指标             | 描述                          |
|------------------|-------------------------------|
| mIoU             | 平均交并比                    |
| F1-score         | 分割准确度综合评价            |
| Precision/Recall | 类别正确率与召回率            |
| 可解释性分析     | CAM、注意力分布可视化结果等  |
