#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
湿地遥感影像分割系统示例运行脚本

这个脚本展示了如何使用整个系统进行湿地分割，包括：
1. 训练模型
2. 测试模型
3. 预测新数据

运行前请确保：
1. 已安装所有依赖包
2. 已准备好数据
3. 已配置好config.yaml文件
"""

import os
import subprocess
import sys
import argparse


def run_command(command, description):
    """运行系统命令"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 执行成功")
        if result.stdout:
            print("输出:")
            print(result.stdout)
    else:
        print("❌ 执行失败")
        print("错误信息:")
        print(result.stderr)
        return False
    
    return True


def check_prerequisites():
    """检查运行前提条件"""
    print("检查运行前提条件...")
    
    # 检查配置文件
    if not os.path.exists('config/config.yaml'):
        print("❌ 配置文件 config/config.yaml 不存在")
        return False
    
    # 检查必要的目录
    required_dirs = ['models', 'data', 'utils', 'config']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            print(f"❌ 目录 {dir_name} 不存在")
            return False
    
    print("✅ 前提条件检查通过")
    return True


def train_example():
    """训练示例"""
    command = """python train.py \\
        --config config/config.yaml \\
        --gpu_ids 0 \\
        --seed 42"""
    
    return run_command(command, "训练湿地分割模型")


def test_example():
    """测试示例"""
    # 查找最新的模型文件
    checkpoint_dir = "checkpoints"
    if os.path.exists(checkpoint_dir):
        # 查找best_model.pth
        model_path = None
        for root, dirs, files in os.walk(checkpoint_dir):
            if 'best_model.pth' in files:
                model_path = os.path.join(root, 'best_model.pth')
                break
        
        if model_path:
            command = f"""python test.py \\
                --config config/config.yaml \\
                --model_path {model_path} \\
                --output_dir test_results/ \\
                --save_predictions \\
                --save_visualizations \\
                --gpu_id 0"""
            
            return run_command(command, "测试训练好的模型")
        else:
            print("❌ 找不到训练好的模型文件")
            return False
    else:
        print("❌ checkpoint目录不存在，请先训练模型")
        return False


def predict_example():
    """预测示例"""
    # 这里需要用户提供实际的数据路径
    s2_path = "data/sentinel2/example_S2.tif"
    s1_path = "data/sentinel1/example_S1.tif"
    
    # 检查数据文件是否存在
    if not os.path.exists(s2_path):
        print(f"❌ Sentinel-2数据文件不存在: {s2_path}")
        print("请将您的Sentinel-2数据放在指定位置，或修改路径")
        return False
    
    if not os.path.exists(s1_path):
        print(f"❌ Sentinel-1数据文件不存在: {s1_path}")
        print("请将您的Sentinel-1数据放在指定位置，或修改路径")
        return False
    
    # 查找模型文件
    checkpoint_dir = "checkpoints"
    model_path = None
    for root, dirs, files in os.walk(checkpoint_dir):
        if 'best_model.pth' in files:
            model_path = os.path.join(root, 'best_model.pth')
            break
    
    if not model_path:
        print("❌ 找不到训练好的模型文件")
        return False
    
    command = f"""python predict.py \\
        --config config/config.yaml \\
        --model_path {model_path} \\
        --s2_path {s2_path} \\
        --s1_path {s1_path} \\
        --output_dir predictions/ \\
        --patch_size 128 \\
        --overlap 32 \\
        --batch_size 16 \\
        --save_confidence \\
        --save_visualization \\
        --gpu_id 0"""
    
    return run_command(command, "预测新的遥感数据")


def main():
    parser = argparse.ArgumentParser(description='湿地遥感影像分割系统示例')
    parser.add_argument('--mode', type=str, choices=['train', 'test', 'predict', 'all'], 
                        default='all', help='运行模式')
    args = parser.parse_args()
    
    print("🌿 湿地遥感影像分割系统示例运行脚本")
    print("=" * 60)
    
    # 检查前提条件
    if not check_prerequisites():
        print("❌ 前提条件检查失败，请检查安装和配置")
        sys.exit(1)
    
    success = True
    
    if args.mode in ['train', 'all']:
        print("\n🚀 开始训练...")
        success &= train_example()
    
    if args.mode in ['test', 'all'] and success:
        print("\n🔍 开始测试...")
        success &= test_example()
    
    if args.mode in ['predict', 'all'] and success:
        print("\n🎯 开始预测...")
        success &= predict_example()
    
    if success:
        print("\n🎉 示例运行完成！")
        print("结果文件位置:")
        print("- 训练结果: checkpoints/")
        print("- 测试结果: test_results/")
        print("- 预测结果: predictions/")
        print("\n请查看相应目录中的结果文件和可视化图像。")
    else:
        print("\n❌ 示例运行中遇到错误，请检查上述错误信息")


if __name__ == '__main__':
    main() 